{"name": "Amazon Scraper API", "description": "亚马逊产品信息提取 Web API 服务", "version": "1.0.0", "author": "Amazon Scraper Team", "python_version": "3.8+", "main_file": "start.py", "requirements_file": "requirements.txt", "environment_variables": {"HOST": "0.0.0.0", "PORT": "8000", "WORKERS": "1", "LOG_LEVEL": "info"}, "startup_command": "python start.py", "health_check_url": "/api/health", "documentation_url": "/api/docs", "dependencies": ["fastapi>=0.104.0", "uvicorn>=0.24.0", "pydantic>=2.5.0", "requests>=2.31.0", "beautifulsoup4>=4.12.0"], "ports": [{"port": 8000, "protocol": "HTTP", "description": "API 服务端口"}], "directories": [{"path": "logs", "description": "日志文件目录", "create_if_not_exists": true}], "files": [{"path": "api.log", "description": "API 服务日志文件", "create_if_not_exists": true}]}