#!/bin/bash

# Amazon Scraper API 启动脚本
# 适用于 Linux 环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查 Python 版本
check_python() {
    log_info "检查 Python 版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        log_error "未找到 Python 解释器"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    log_info "Python 版本: $PYTHON_VERSION"
    
    # 检查版本是否满足要求 (3.8+)
    MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 8 ]); then
        log_error "Python 版本过低，需要 3.8 或更高版本"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p temp
    
    log_info "目录创建完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装 Python 依赖..."
    
    if [ -f "requirements.txt" ]; then
        $PYTHON_CMD -m pip install -r requirements.txt
        log_info "依赖安装完成"
    else
        log_warn "未找到 requirements.txt 文件"
    fi
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "main.py" ]; then
        log_error "未找到 main.py 文件"
        exit 1
    fi
    
    if [ ! -f "amazon_scraper.py" ]; then
        log_error "未找到 amazon_scraper.py 文件"
        exit 1
    fi
    
    if [ ! -f "scraper_adapter.py" ]; then
        log_error "未找到 scraper_adapter.py 文件"
        exit 1
    fi
    
    log_info "配置文件检查完成"
}

# 启动服务
start_service() {
    log_info "启动 Amazon Scraper API 服务..."
    
    # 设置环境变量
    export HOST=${HOST:-"0.0.0.0"}
    export PORT=${PORT:-8000}
    export WORKERS=${WORKERS:-1}
    export LOG_LEVEL=${LOG_LEVEL:-"info"}
    
    log_info "服务配置:"
    log_info "  主机: $HOST"
    log_info "  端口: $PORT"
    log_info "  工作进程: $WORKERS"
    log_info "  日志级别: $LOG_LEVEL"
    
    # 选择启动方式
    if command -v gunicorn &> /dev/null && [ -f "gunicorn.conf.py" ]; then
        log_info "使用 Gunicorn 启动服务..."
        gunicorn -c gunicorn.conf.py main:app
    else
        log_info "使用 Uvicorn 启动服务..."
        $PYTHON_CMD start.py
    fi
}

# 主函数
main() {
    log_info "Amazon Scraper API 启动脚本"
    log_info "================================"
    
    check_python
    create_directories
    install_dependencies
    check_config
    start_service
}

# 处理命令行参数
case "${1:-start}" in
    start)
        main
        ;;
    install)
        check_python
        install_dependencies
        ;;
    check)
        check_python
        check_config
        log_info "所有检查通过"
        ;;
    *)
        echo "用法: $0 {start|install|check}"
        echo "  start   - 启动服务（默认）"
        echo "  install - 仅安装依赖"
        echo "  check   - 仅检查环境"
        exit 1
        ;;
esac
