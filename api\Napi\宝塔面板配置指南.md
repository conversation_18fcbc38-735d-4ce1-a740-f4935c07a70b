# 宝塔面板 Amazon Scraper API 配置指南

## 快速配置步骤

### 1. 环境准备

**检查宝塔面板版本**
- 宝塔面板版本: 7.0+
- Python 项目管理器: 已安装

**安装 Python 项目管理器**
```
宝塔面板 -> 软件商店 -> 搜索 "Python项目管理器" -> 安装
```

### 2. 创建 Python 项目

**项目配置信息**
```
项目名称: amazon-scraper-api
项目路径: /www/wwwroot/amazon-scraper-api
Python版本: 3.8+ (推荐 3.9 或 3.10)
框架: 其他
启动方式: python
启动文件: start.py
端口: 8000
```

**详细步骤**
1. 宝塔面板 -> Python项目管理 -> 添加Python项目
2. 填写上述配置信息
3. 点击 "提交" 创建项目

### 3. 上传项目文件

**必需文件列表**
- ✅ `main.py` - FastAPI 主应用
- ✅ `amazon_scraper.py` - 爬虫核心逻辑
- ✅ `scraper_adapter.py` - 适配器
- ✅ `start.py` - 启动脚本
- ✅ `requirements.txt` - 依赖列表

**上传方式**
- 方式1: 通过宝塔面板文件管理器上传
- 方式2: 通过 FTP 工具上传
- 方式3: 通过 Git 克隆

### 4. 安装依赖

**通过宝塔面板安装**
1. 进入项目详情页
2. 点击 "模块" 标签
3. 点击 "安装模块" -> "从requirements.txt安装"
4. 等待安装完成

**手动安装 (如果自动安装失败)**
```bash
cd /www/wwwroot/amazon-scraper-api
source venv/bin/activate
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 5. 环境变量配置

在项目设置中添加环境变量:

| 变量名 | 值 | 说明 |
|--------|----|----- |
| HOST | 0.0.0.0 | 服务监听地址 |
| PORT | 8000 | 服务端口 |
| WORKERS | 1 | 工作进程数 |
| LOG_LEVEL | info | 日志级别 |

### 6. 启动项目

1. 在项目列表中找到 `amazon-scraper-api`
2. 点击 "启动" 按钮
3. 等待状态变为 "运行中"
4. 查看日志确认启动成功

### 7. 测试访问

**基本测试**
```
健康检查: http://服务器IP:8000/api/health
API文档: http://服务器IP:8000/api/docs
```

**测试命令**
```bash
# 健康检查
curl http://localhost:8000/api/health

# 查看API文档
curl http://localhost:8000/api/docs
```

## 域名配置 (可选)

### 1. 创建网站

```
域名: api.yourdomain.com
根目录: /www/wwwroot/amazon-scraper-api
PHP版本: 纯静态
```

### 2. 配置反向代理

**Nginx 配置**
```nginx
location / {
    proxy_pass http://127.0.0.1:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
}
```

### 3. SSL 证书 (推荐)

1. 网站设置 -> SSL -> Let's Encrypt
2. 申请免费 SSL 证书
3. 开启强制 HTTPS

## 常见问题快速解决

### 问题 1: 启动失败

**症状**: 项目状态显示 "已停止"

**解决步骤**:
1. 查看项目日志
2. 检查端口是否被占用
3. 验证 Python 版本
4. 检查依赖是否安装完整

**命令检查**:
```bash
# 检查端口占用
netstat -tlnp | grep :8000

# 手动启动测试
cd /www/wwwroot/amazon-scraper-api
source venv/bin/activate
python start.py
```

### 问题 2: 依赖安装失败

**症状**: 模块安装时报错

**解决方案**:
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 升级 pip
pip install --upgrade pip

# 逐个安装核心依赖
pip install fastapi==0.104.1
pip install uvicorn[standard]==0.24.0
pip install pydantic==2.5.0
pip install requests==2.31.0
pip install beautifulsoup4==4.12.2
```

### 问题 3: 权限错误

**症状**: 文件权限不足

**解决方案**:
```bash
# 设置正确权限
chown -R www:www /www/wwwroot/amazon-scraper-api
chmod -R 755 /www/wwwroot/amazon-scraper-api

# 创建日志目录
mkdir -p /www/wwwroot/amazon-scraper-api/logs
chown www:www /www/wwwroot/amazon-scraper-api/logs
```

### 问题 4: 内存不足

**症状**: 服务频繁重启或崩溃

**解决方案**:
1. 减少 WORKERS 环境变量为 1
2. 监控内存使用情况
3. 考虑升级服务器配置

## 性能优化建议

### 1. 服务器配置

**最低配置**:
- CPU: 1核
- 内存: 1GB
- 磁盘: 10GB

**推荐配置**:
- CPU: 2核+
- 内存: 2GB+
- 磁盘: 20GB+

### 2. 应用优化

**环境变量调优**:
```
WORKERS=1          # 单核服务器
WORKERS=2          # 双核服务器
LOG_LEVEL=warning  # 生产环境减少日志
```

### 3. 监控设置

**日志轮转**:
```bash
# 添加到 crontab
0 0 * * * find /www/wwwroot/amazon-scraper-api/logs -name "*.log" -size +100M -delete
```

**监控脚本**:
```bash
#!/bin/bash
# 检查服务状态
if ! curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
    echo "API服务异常，尝试重启..."
    # 重启逻辑
fi
```

## 安全配置

### 1. 防火墙设置

```bash
# 只允许特定IP访问
iptables -A INPUT -p tcp --dport 8000 -s 允许的IP -j ACCEPT
iptables -A INPUT -p tcp --dport 8000 -j DROP
```

### 2. Nginx 安全配置

```nginx
# 限制请求大小
client_max_body_size 10M;

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;

# 隐藏服务器信息
server_tokens off;
```

## 备份和恢复

### 1. 备份脚本

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/amazon-scraper-api"
PROJECT_DIR="/www/wwwroot/amazon-scraper-api"

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C $PROJECT_DIR .
```

### 2. 恢复步骤

```bash
# 停止服务
# 解压备份文件
tar -xzf backup_20250815_120000.tar.gz -C /www/wwwroot/amazon-scraper-api/
# 重新安装依赖
# 启动服务
```

## 联系支持

如遇到问题，请提供以下信息：
1. 宝塔面板版本
2. Python 版本
3. 错误日志内容
4. 服务器配置信息
