#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Amazon Scraper API 启动脚本
适用于宝塔面板 Python 项目部署
"""

import os
import sys
import uvicorn
from main import app

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def main():
    """主启动函数"""
    # 从环境变量获取配置，如果没有则使用默认值
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 8000))
    workers = int(os.getenv('WORKERS', 1))
    log_level = os.getenv('LOG_LEVEL', 'info')
    
    print(f"启动 Amazon Scraper API 服务")
    print(f"主机: {host}")
    print(f"端口: {port}")
    print(f"工作进程: {workers}")
    print(f"日志级别: {log_level}")
    
    # 启动服务
    uvicorn.run(
        app,
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        access_log=True
    )

if __name__ == "__main__":
    main()
