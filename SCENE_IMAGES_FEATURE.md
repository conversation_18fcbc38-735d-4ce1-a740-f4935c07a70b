# Amazon Scraper 场景图提取功能

## 功能概述

为 `amazon_scraper.py` 脚本新增了场景图数据提取功能，能够从Amazon产品页面的产品信息模块中提取场景图片，并将其作为 `scene` 字段发送到API。

## 实现的功能

### 1. 场景图提取
- **提取位置**: 从 `#aplus_feature_div .celwidget` 模块中提取
- **识别规则**: 查找包含以下关键词的模块标题：
  - `Produktinformation` (德语)
  - `Product Information` (英语)
  - `Product Details` (英语)
  - `Informations sur le produit` (法语)
  - `Produktbeschreibung des Herstellers` (德语)
  - `Información del producto` (西班牙语)

### 2. 图片提取规则
- **优先级**: 优先使用 `data-src` 属性，如果不存在则使用 `src` 属性
- **过滤机制**: 自动过滤掉以下类型的无效图片：
  - sprite 图片
  - 像素图片 (pixel.gif, grey-pixel)
  - 占位图片 (placeholder)
  - 加载图片 (loading)
  - 空白图片 (blank)
  - 1x1像素图片
  - 透明图片 (transparent)

### 3. 数据格式
- **存储格式**: 数组形式存储图片URL
- **API格式**: JSON数组字符串
- **示例格式**:
  ```json
  ["https://m.media-amazon.com/images/I/517N%20dnQG5L.jpg","https://m.media-amazon.com/images/I/61uCS-Ze1sL.jpg"]
  ```

## 代码修改详情

### 1. 数据结构修改
在 `product_data` 中新增了 `场景图` 字段：
```python
self.product_data = {
    # ... 其他字段
    "场景图": [],  # 新增场景图字段
    # ... 其他字段
}
```

### 2. 新增方法

#### `_extract_scene_images()`
主要的场景图提取方法，实现了与JavaScript代码相同的提取逻辑。

#### `_is_valid_scene_image(url)`
验证场景图URL有效性的辅助方法，过滤无效图片。

### 3. API数据修改
在 `send_to_api()` 方法中新增了 `scene` 字段：
```python
post_data = {
    # ... 其他字段
    'scene': json.dumps(self.product_data['场景图'], ensure_ascii=False),
    # ... 其他字段
}
```

### 4. 响应数据修改
在API响应的产品信息中新增了 `scene` 字段：
```python
product_info = {
    # ... 其他字段
    "scene": scraper.product_data.get("场景图", []),
    # ... 其他字段
}
```

## 使用方法

### 1. 通过API调用
发送POST请求到 `/api/scrape` 端点，脚本会自动提取场景图并包含在响应中：

```json
{
  "success": true,
  "data": {
    "title": "产品标题",
    "images": ["主图URL1", "主图URL2"],
    "scene": ["场景图URL1", "场景图URL2"],
    // ... 其他字段
  }
}
```

### 2. 直接使用类
```python
from amazon_scraper import AmazonProductScraper

scraper = AmazonProductScraper(html_content=html_content)
if scraper.extract_product_info():
    scene_images = scraper.product_data['场景图']
    print(f"提取到 {len(scene_images)} 张场景图")
```

## 测试验证

### 1. 功能测试
- `test_scene_extraction.py`: 验证场景图提取功能
- `test_api_integration.py`: 验证API数据结构

### 2. 测试结果
- ✅ 场景图提取功能正常
- ✅ 无效图片过滤正常
- ✅ API数据格式正确
- ✅ 与JavaScript示例兼容

## 兼容性

- **向后兼容**: 不影响现有功能
- **多语言支持**: 支持德语、英语、法语、西班牙语关键词
- **格式兼容**: 与JavaScript提取逻辑完全一致

## 调试信息

脚本会在详细模式下输出以下调试信息：
- 找到的aplus模块数量
- 匹配的产品信息模块
- 提取的场景图数量
- 跳过的无效图片

## 注意事项

1. 场景图提取依赖于Amazon页面的aplus模块结构
2. 如果页面没有产品信息模块，场景图数组将为空
3. 场景图与主产品图片是分开存储和处理的
4. API发送时场景图以JSON数组格式传输
