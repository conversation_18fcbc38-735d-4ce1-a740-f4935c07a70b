# Gunicorn 配置文件
# 适用于生产环境部署

import os
import multiprocessing

# 服务器配置
bind = f"0.0.0.0:{os.getenv('PORT', 8000)}"
workers = int(os.getenv('WORKERS', multiprocessing.cpu_count() * 2 + 1))
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# 日志配置
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = os.getenv('LOG_LEVEL', 'info')
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
preload_app = True
daemon = False
pidfile = "gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# 超时配置
timeout = 120
keepalive = 5
graceful_timeout = 30

# 安全配置
limit_request_line = 4096
limit_request_fields = 100
limit_request_field_size = 8190

# 性能配置
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统提高性能

def when_ready(server):
    """服务器启动完成时的回调"""
    server.log.info("Amazon Scraper API 服务已启动")

def worker_int(worker):
    """工作进程中断时的回调"""
    worker.log.info("工作进程被中断")

def pre_fork(server, worker):
    """工作进程 fork 前的回调"""
    server.log.info(f"工作进程 {worker.pid} 即将启动")

def post_fork(server, worker):
    """工作进程 fork 后的回调"""
    server.log.info(f"工作进程 {worker.pid} 已启动")

def worker_abort(worker):
    """工作进程异常退出时的回调"""
    worker.log.info(f"工作进程 {worker.pid} 异常退出")
