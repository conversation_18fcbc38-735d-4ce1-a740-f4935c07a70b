<?php
/**
 * 接口: GET/POST /crawl/updateCaptureState.php
 * 功能: 更新 crawl_html 表中记录的处理状态
 * 参数:
 *   id            记录ID (可选，与warehouse_id二选一)
 *   warehouse_id  仓库ID (可选，与id二选一，用于批量更新)
 *   state         状态值 (必填): 0=待处理, 1=处理中, 2=成功, 3=失败
 *   from_state    原状态值 (可选，仅在warehouse_id模式下使用，指定要更新的原状态)
 *   msg           处理消息 (可选)
 * 
 * 使用模式:
 *   1. 单个记录更新: 传id + state
 *   2. 批量更新: 传warehouse_id + state + from_state (例如: 将仓库3082中状态为3的记录改为0)
 * 
 * 返回:
 *   code 0   更新成功
 *   其他      错误
 */

declare(strict_types=1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

require_once __DIR__ . '/db.php';
$pdo = get_pdo();

	// 简单错误日志函数
	function log_update_capture_error(string $phase, $context): void {
		$line = sprintf('[%s] %s %s%s', date('Y-m-d H:i:s'), $phase, json_encode($context, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), PHP_EOL);
		error_log($line, 3, __DIR__ . '/updateCaptureState_error.log');
	}

$method =  $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $warehouseId = isset($_GET['warehouse_id']) ? (int)$_GET['warehouse_id'] : 0;
		$minSales = isset($_GET['min_sales']) ? (int)$_GET['min_sales'] : (isset($_GET['sales']) ? (int)$_GET['sales'] : 5);
		$countType = isset($_GET['count_type']) ? (string)$_GET['count_type'] : 'status_sales'; // status_sales | status_only
		$onlyStatus = isset($_GET['only_status']) ? (int)$_GET['only_status'] : 0; // 兼容开关
		$targetStatus = isset($_GET['target_status']) ? (int)$_GET['target_status'] : null; // 指定要统计的状态值（如 3）
		if ($onlyStatus === 1) {
			$countType = 'status_only';
		}
    if ($warehouseId <= 0) {
        http_response_code(400);
        echo json_encode(['code' => -1, 'msg' => 'warehouse_id参数必填且必须为正整数']);
        exit;
    }

	try {
        // 统计规则说明：
        // - 如果显式传入 target_status，则按该状态统计（例如 target_status=3）。
        // - 如果没有传 target_status，但明确传入 min_sales（例如 ?min_sales=5），
        //   则按 status=2 且 sales > min_sales 进行统计（满足只传 min_sales 时查询状态为2的需求）。
        // - 否则如果 count_type === 'status_only'，仅按 status = 0 统计（忽略销量）。
        // - 默认（未传 target_status 且未明确传 min_sales）为 status = 0 且 sales > min_sales。
        if ($targetStatus !== null) {
            $sql = 'SELECT COUNT(*) AS cnt FROM amazon_products WHERE warehouse_id = :wid AND status = :st';
            $params = [':wid' => $warehouseId, ':st' => $targetStatus];
        } elseif (isset($_GET['min_sales'])) {
            // 明确传入 min_sales 且未传 target_status 时，按 status=2 查询（用户需求）
            $sql = 'SELECT COUNT(*) AS cnt FROM amazon_products WHERE warehouse_id = :wid AND status = 2 AND sales > :min_sales';
            $params = [':wid' => $warehouseId, ':min_sales' => $minSales];
        } elseif ($countType === 'status_only') {
            $sql = 'SELECT COUNT(*) AS cnt FROM amazon_products WHERE warehouse_id = :wid AND status = 0';
            $params = [':wid' => $warehouseId];
        } else {
            $sql = 'SELECT COUNT(*) AS cnt FROM amazon_products WHERE warehouse_id = :wid AND status = 0 AND sales > :min_sales';
            $params = [':wid' => $warehouseId, ':min_sales' => $minSales];
        }
		$stmt = $pdo->prepare($sql);
		$stmt->execute($params);
		$count = (int)$stmt->fetchColumn();

		$response = [
			'code' => 0,
			'msg'  => 'success',
			'data' => ['count' => $count],
		];
		echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	} catch (Throwable $e) {
		log_update_capture_error('GET_ERROR', [
			'warehouse_id' => $warehouseId,
			'min_sales' => $minSales,
			'count_type' => $countType,
			'target_status' => $targetStatus,
			'sql' => $sql ?? null,
			'params' => $params ?? null,
			'error' => $e->getMessage()
		]);
        http_response_code(500);
        echo json_encode(['code' => -1, 'msg' => '数据库错误', 'error' => $e->getMessage()]);
    }
    exit;
}

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => -1, 'msg' => 'Method Not Allowed']);
    exit;
}

// 解析请求数据
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
$inputData = [];

if (stripos($contentType, 'application/json') !== false) {
    // JSON 格式
    $raw = file_get_contents('php://input');
    $inputData = json_decode($raw, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode(['code' => -1, 'msg' => 'Invalid JSON']);
        exit;
    }
} else {
    // 表单格式
    $inputData = $_POST;
}

// 参数验证
$recordId = isset($inputData['id']) ? (int)$inputData['id'] : 0;
$warehouseId = isset($inputData['warehouse_id']) ? (int)$inputData['warehouse_id'] : 0;
$state = isset($inputData['state']) ? (int)$inputData['state'] : -1;
$fromState = isset($inputData['from_state']) ? (int)$inputData['from_state'] : -1;
$msg = $inputData['msg'] ?? '';

// 验证操作模式
if ($recordId <= 0 && $warehouseId <= 0) {
    http_response_code(400);
    echo json_encode(['code' => -1, 'msg' => 'id或warehouse_id必须提供其中一个']);
    exit;
}

if ($recordId > 0 && $warehouseId > 0) {
    http_response_code(400);
    echo json_encode(['code' => -1, 'msg' => 'id和warehouse_id不能同时提供，请选择其中一个']);
    exit;
}

if ($state < 0 || $state > 3) {
    http_response_code(400);
    echo json_encode(['code' => -1, 'msg' => 'state必填，有效值为: 0=待处理, 1=处理中, 2=成功, 3=失败']);
    exit;
}

// 批量更新模式的额外验证
if ($warehouseId > 0 && $fromState < 0) {
    http_response_code(400);
    echo json_encode(['code' => -1, 'msg' => '批量更新模式下，from_state参数必填(指定要更新的原状态)']);
    exit;
}

if ($fromState >= 0 && ($fromState < 0 || $fromState > 3)) {
    http_response_code(400);
    echo json_encode(['code' => -1, 'msg' => 'from_state有效值为: 0=待处理, 1=处理中, 2=成功, 3=失败']);
    exit;
}

try {
    if ($recordId > 0) {
        // 单个记录更新模式
        $sql = "UPDATE crawl_html SET capture_state = :state, updated_at = NOW() WHERE id = :id";
        $params = [
            ':state' => $state,
            ':id' => $recordId
        ];
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'code' => 0, 
                'msg' => '单个记录更新成功', 
                'data' => [
                    'mode' => 'single',
                    'id' => $recordId,
                    'new_state' => $state,
                    'affected_rows' => $stmt->rowCount()
                ],
                'process_msg' => $msg
            ]);
        } else {
            echo json_encode(['code' => 1, 'msg' => '没有记录被更新 (记录不存在?)']);
        }
    } else {
        // 批量更新模式 (按仓库ID)
        $sql = "UPDATE crawl_html SET capture_state = :state, updated_at = NOW() WHERE warehouse_id = :warehouse_id AND capture_state = :from_state";
        $params = [
            ':state' => $state,
            ':warehouse_id' => $warehouseId,
            ':from_state' => $fromState
        ];
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'code' => 0, 
                'msg' => '批量更新成功', 
                'data' => [
                    'mode' => 'batch',
                    'warehouse_id' => $warehouseId,
                    'from_state' => $fromState,
                    'to_state' => $state,
                    'affected_rows' => $stmt->rowCount()
                ],
                'process_msg' => $msg
            ]);
        } else {
            echo json_encode(['code' => 1, 'msg' => '没有记录被更新 (可能没有符合条件的记录)']);
        }
    }

} catch (Throwable $e) {
    http_response_code(500);
    echo json_encode(['code' => -1, 'msg' => '数据库错误', 'error' => $e->getMessage()]);
} 