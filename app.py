#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WSGI入口文件 - 用于宝塔面板部署
Amazon Scraper API 服务
"""

from amazon_scraper import app

if __name__ == "__main__":
    # 开发环境直接运行
    print("启动 Amazon Scraper API 服务...")
    print("API 端点:")
    print("  - POST /api/scrape - 处理Amazon HTML内容")
    print("  - GET /api/health - 健康检查")
    print("  - GET / - API说明")
    print(f"服务运行在: http://0.0.0.0:6677")
    app.run(host='0.0.0.0', port=1122, debug=False)
else:
    # 生产环境通过WSGI服务器运行
    application = app