#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试multipart/form-data优化效果的脚本
用于验证amazon_scraper.py的性能改进
"""

import requests
import time
import os
import base64

def create_test_html_content():
    """创建测试用的HTML内容（约2MB）"""
    # 基础Amazon产品页面结构
    base_html = """
    <!DOCTYPE html>
    <html lang="de">
    <head>
        <title>Test Amazon Product</title>
        <meta charset="utf-8">
    </head>
    <body>
        <div id="dp-container">
            <h1 id="productTitle">Test Amazon Product Title - Optimized Version</h1>
            <div id="bylineInfo">
                <a href="/stores/TestBrand/page/">Visit the TestBrand Store</a>
            </div>
            <div class="a-price">
                <span class="a-offscreen">€29.99</span>
            </div>
            <div id="feature-bullets">
                <ul>
                    <li>Test feature 1</li>
                    <li>Test feature 2</li>
                </ul>
            </div>
            <div id="aplus_feature_div">
                <div class="celwidget">
                    <h2>Produktbeschreibung des Herstellers</h2>
                    <img src="https://example.com/scene1.jpg" alt="Scene 1">
                </div>
            </div>
        </div>
        
        <!-- 填充内容以达到约2MB大小 -->
        <div id="filler-content">
    """
    
    # 添加大量填充内容以达到2MB
    filler_content = ""
    target_size = 2 * 1024 * 1024  # 2MB
    
    # 重复添加内容直到达到目标大小
    base_content = "<p>This is filler content to reach 2MB size. " * 100 + "</p>\n"
    
    while len(base_html + filler_content + "</div></body></html>") < target_size:
        filler_content += base_content
    
    full_html = base_html + filler_content + "</div></body></html>"
    
    print(f"生成测试HTML内容，大小: {len(full_html)} 字节 ({len(full_html)/1024/1024:.2f} MB)")
    return full_html

def test_multipart_request(api_url, html_content):
    """测试multipart/form-data请求"""
    print(f"\n{'='*60}")
    print(f"测试multipart/form-data请求优化效果")
    print(f"{'='*60}")
    
    # 准备multipart数据
    files = {
        'pageContent': ('test.html', html_content, 'text/html')
    }
    
    data = {
        'warehouse_id': '3079',
        'source_link': 'https://amazon.de/dp/TEST123',
        'send_to_api': 'false',  # 不发送到WMS API以专注测试解析性能
        'enable_variants': 'true',
        'verbose_debug': 'true',
        'price': '29.99'
    }
    
    print(f"请求数据大小: {len(html_content)} 字节")
    print(f"目标API: {api_url}")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            api_url,
            files=files,
            data=data,
            timeout=30
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n📊 性能测试结果:")
        print(f"总请求时间: {processing_time:.2f}秒")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 请求成功")
                print(f"数据提取成功: {result.get('success', False)}")
                
                # 检查调试信息中的性能数据
                debug_info = result.get('debug_info', '')
                if debug_info:
                    # 提取性能相关信息
                    lines = debug_info.split('\n')
                    for line in lines:
                        if '[PERFORMANCE]' in line:
                            print(f"🚀 {line}")
                        elif '[SUCCESS]' in line and 'API处理时间优化成功' in line:
                            print(f"🎉 {line}")
                        elif '[WARNING]' in line and 'API处理时间过长' in line:
                            print(f"⚠️ {line}")
                
                # 检查提取的数据
                data_info = result.get('data', {})
                if data_info:
                    print(f"\n📋 提取的产品信息:")
                    print(f"标题: {data_info.get('title', 'N/A')[:50]}...")
                    print(f"价格: {data_info.get('price', 'N/A')}")
                    print(f"图片数量: {len(data_info.get('images', []))}")
                    print(f"变体数量: {len(data_info.get('variants', []))}")
                
            except Exception as e:
                print(f"❌ 响应解析失败: {e}")
                print(f"响应内容前500字符: {response.text[:500]}")
        else:
            print(f"❌ 请求失败")
            print(f"响应内容: {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"⏰ 请求超时 (耗时: {processing_time:.2f}秒)")
        
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"❌ 请求异常: {e} (耗时: {processing_time:.2f}秒)")

def test_performance_comparison():
    """性能对比测试"""
    print(f"\n{'='*60}")
    print(f"性能优化对比测试")
    print(f"{'='*60}")
    
    # API端点
    api_url = "http://localhost:5000/api/scrape"
    
    # 创建测试内容
    html_content = create_test_html_content()
    
    # 测试优化后的multipart请求
    test_multipart_request(api_url, html_content)
    
    print(f"\n{'='*60}")
    print(f"优化目标验证:")
    print(f"✅ 目标: API处理时间 < 5秒")
    print(f"✅ 目标: 移除不必要的JSON解析层级")
    print(f"✅ 目标: 优化multipart/form-data处理")
    print(f"{'='*60}")

if __name__ == "__main__":
    print("Amazon Scraper API 性能优化测试")
    print("=" * 60)
    
    # 检查API是否运行
    try:
        health_response = requests.get("http://localhost:5000/api/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ API服务正在运行")
            test_performance_comparison()
        else:
            print("❌ API服务未正常响应")
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        print("请确保amazon_scraper.py正在运行在localhost:5000")
