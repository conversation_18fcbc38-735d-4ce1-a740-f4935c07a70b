# Amazon Scraper Web API

基于 FastAPI 的亚马逊产品信息提取 Web API 服务，支持宝塔面板部署。

## 功能特性

- 🚀 **高性能**: 基于 FastAPI 框架，支持异步处理
- 📊 **数据提取**: 提取亚马逊产品标题、价格、图片、变体等信息
- 🔄 **API 集成**: 支持将提取的数据发送到指定 API 接口
- 📝 **数据验证**: 完整的输入输出数据验证
- 📋 **自动文档**: 自动生成 API 文档
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🔧 **易部署**: 支持宝塔面板一键部署

## 项目结构

```
Amazon-Scraper-API/
├── main.py                 # FastAPI 主应用文件
├── amazon_scraper.py       # 原有的爬虫逻辑
├── scraper_adapter.py      # 爬虫适配器
├── start.py               # 启动脚本
├── requirements.txt       # Python 依赖
├── gunicorn.conf.py      # Gunicorn 配置
├── bt_config.json        # 宝塔面板配置
├── run.sh                # Linux 启动脚本
├── run.bat               # Windows 启动脚本
└── README.md             # 项目文档
```

## 宝塔面板部署指南

### 1. 环境要求

- **Python 版本**: 3.8 或更高
- **宝塔面板**: 7.0 或更高版本
- **内存**: 建议 1GB 以上
- **磁盘空间**: 建议 500MB 以上

### 2. 创建 Python 项目

1. 登录宝塔面板
2. 点击左侧菜单 **"Python项目管理"**
3. 点击 **"添加Python项目"**
4. 填写项目信息：
   - **项目名称**: `amazon-scraper-api`
   - **项目路径**: `/www/wwwroot/amazon-scraper-api`
   - **Python版本**: 选择 3.8 或更高版本
   - **框架**: 选择 `其他`
   - **启动方式**: `python`
   - **启动文件**: `start.py`
   - **端口**: `8000`（或其他可用端口）

### 3. 上传项目文件

将以下文件上传到项目目录：
- `main.py`
- `amazon_scraper.py`
- `scraper_adapter.py`
- `start.py`
- `requirements.txt`
- `gunicorn.conf.py`

### 4. 安装依赖

在宝塔面板的 Python 项目管理中：

1. 点击项目名称进入项目详情
2. 点击 **"模块"** 标签
3. 点击 **"安装模块"**
4. 选择 **"从requirements.txt安装"**
5. 等待安装完成

或者通过终端安装：
```bash
cd /www/wwwroot/amazon-scraper-api
pip install -r requirements.txt
```

### 5. 配置项目

#### 5.1 环境变量配置

在宝塔面板项目设置中添加环境变量：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `HOST` | `0.0.0.0` | 服务监听地址 |
| `PORT` | `8000` | 服务端口 |
| `WORKERS` | `1` | 工作进程数 |
| `LOG_LEVEL` | `info` | 日志级别 |

#### 5.2 启动配置

- **启动文件**: `start.py`
- **启动命令**: `python start.py`
- **运行目录**: 项目根目录

### 6. 启动项目

1. 在宝塔面板中点击 **"启动"** 按钮
2. 查看 **"日志"** 确认启动成功
3. 访问 `http://你的域名:8000/api/docs` 查看 API 文档

### 7. 配置反向代理（可选）

如果需要通过域名访问，可以配置 Nginx 反向代理：

1. 在宝塔面板创建网站
2. 配置 Nginx 反向代理：

```nginx
location / {
    proxy_pass http://127.0.0.1:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## API 使用说明

### 主要端点

#### 1. 健康检查
```
GET /api/health
```

#### 2. 提取产品信息
```
POST /api/scrape
```

**请求体示例**:
```json
{
  "html_content": "<html>Amazon商品页面HTML内容</html>",
  "warehouse_id": "3079",
  "api_url": "http://**************/wms/product/importProducts",
  "currency": "EUR",
  "source_link": "https://amazon.de/dp/B123456789",
  "send_to_api": true,
  "verbose": false
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "产品信息提取成功",
  "product_info": {
    "title": "产品标题",
    "price": "29.99",
    "sku": "B123456789",
    "images": ["https://image1.jpg", "https://image2.jpg"],
    "description": "产品描述",
    "variants": []
  },
  "api_result": {
    "success": true,
    "status_code": 200
  },
  "processing_time": 2.5,
  "timestamp": "2025-08-15T10:30:00"
}
```

#### 3. 验证 HTML 内容
```
POST /api/validate-html
```

#### 4. 批量处理
```
POST /api/batch-scrape
```

#### 5. 服务统计
```
GET /api/stats
```

### API 文档

启动服务后，可以通过以下地址访问 API 文档：
- **Swagger UI**: `http://你的域名:8000/api/docs`
- **ReDoc**: `http://你的域名:8000/api/redoc`

## 使用示例

### Python 客户端示例

```python
import requests

# API 基础地址
base_url = "http://你的域名:8000"

# 读取 HTML 文件
with open("amazon_product.html", "r", encoding="utf-8") as f:
    html_content = f.read()

# 发送请求
response = requests.post(f"{base_url}/api/scrape", json={
    "html_content": html_content,
    "warehouse_id": "3079",
    "send_to_api": True,
    "verbose": False
})

# 处理响应
if response.status_code == 200:
    result = response.json()
    if result["success"]:
        print("提取成功!")
        print(f"产品标题: {result['product_info']['title']}")
        print(f"产品价格: {result['product_info']['price']}")
    else:
        print(f"提取失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

### JavaScript 客户端示例

```javascript
const baseUrl = "http://你的域名:8000";

async function scrapeProduct(htmlContent) {
    try {
        const response = await fetch(`${baseUrl}/api/scrape`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                html_content: htmlContent,
                warehouse_id: "3079",
                send_to_api: true,
                verbose: false
            })
        });

        const result = await response.json();
        
        if (result.success) {
            console.log("提取成功!", result.product_info);
        } else {
            console.error("提取失败:", result.message);
        }
    } catch (error) {
        console.error("请求错误:", error);
    }
}
```

## 故障排除

### 常见问题

1. **启动失败**
   - 检查 Python 版本是否满足要求
   - 检查依赖是否正确安装
   - 查看错误日志

2. **端口冲突**
   - 修改 `PORT` 环境变量
   - 检查端口是否被占用

3. **内存不足**
   - 减少 `WORKERS` 数量
   - 增加服务器内存

4. **依赖安装失败**
   - 检查网络连接
   - 使用国内 pip 镜像源

### 日志查看

- **应用日志**: `logs/api.log`
- **访问日志**: `logs/access.log`
- **错误日志**: `logs/error.log`

## 性能优化

1. **调整工作进程数**: 根据 CPU 核心数调整 `WORKERS`
2. **使用 Gunicorn**: 生产环境建议使用 Gunicorn
3. **配置缓存**: 可以添加 Redis 缓存提高性能
4. **负载均衡**: 多实例部署时配置负载均衡

## 安全建议

1. **限制访问**: 配置防火墙限制访问来源
2. **HTTPS**: 生产环境使用 HTTPS
3. **API 密钥**: 添加 API 密钥验证
4. **速率限制**: 配置请求速率限制

## 技术支持

如有问题，请检查：
1. 项目日志文件
2. 宝塔面板错误日志
3. Python 项目运行状态

## 宝塔面板详细配置步骤

### 步骤 1: 准备工作

1. **确保宝塔面板已安装 Python 管理器**
   - 在宝塔面板 -> 软件商店 -> 搜索 "Python项目管理器"
   - 如未安装，点击安装

2. **检查 Python 版本**
   - 进入 Python 项目管理器
   - 确保有 Python 3.8+ 版本可用
   - 如没有，点击 "版本管理" -> "安装" 安装所需版本

### 步骤 2: 创建项目

1. **添加 Python 项目**
   ```
   项目名称: amazon-scraper-api
   项目路径: /www/wwwroot/amazon-scraper-api
   Python版本: 3.8+ (选择已安装的版本)
   框架: 其他
   启动方式: python
   启动文件: start.py
   端口: 8000
   ```

2. **项目创建后的目录结构**
   ```
   /www/wwwroot/amazon-scraper-api/
   ├── venv/                 # 虚拟环境(自动创建)
   ├── logs/                 # 日志目录(需要创建)
   └── 项目文件...
   ```

### 步骤 3: 上传文件

将以下文件上传到 `/www/wwwroot/amazon-scraper-api/` 目录：

**必需文件**:
- `main.py` - FastAPI 主应用
- `amazon_scraper.py` - 原有爬虫逻辑
- `scraper_adapter.py` - 适配器
- `start.py` - 启动脚本
- `requirements.txt` - 依赖列表

**可选文件**:
- `gunicorn.conf.py` - 生产环境配置
- `bt_config.json` - 宝塔配置文件

### 步骤 4: 安装依赖

**方法一: 通过宝塔面板**
1. 进入项目详情页
2. 点击 "模块" 标签
3. 点击 "安装模块"
4. 选择 "从requirements.txt安装"
5. 等待安装完成

**方法二: 通过终端**
```bash
cd /www/wwwroot/amazon-scraper-api
source venv/bin/activate  # 激活虚拟环境
pip install -r requirements.txt
```

### 步骤 5: 配置环境变量

在宝塔面板项目设置中添加：

| 环境变量 | 值 | 说明 |
|----------|----|----- |
| HOST | 0.0.0.0 | 监听地址 |
| PORT | 8000 | 端口号 |
| WORKERS | 1 | 工作进程数 |
| LOG_LEVEL | info | 日志级别 |

### 步骤 6: 启动项目

1. 在项目列表中找到 `amazon-scraper-api`
2. 点击 "启动" 按钮
3. 查看 "日志" 确认启动状态
4. 状态显示 "运行中" 表示启动成功

### 步骤 7: 测试访问

1. **健康检查**: `http://服务器IP:8000/api/health`
2. **API 文档**: `http://服务器IP:8000/api/docs`

### 步骤 8: 配置域名访问 (可选)

1. **创建网站**
   - 在宝塔面板 -> 网站 -> 添加站点
   - 域名: `api.yourdomain.com`
   - 根目录: `/www/wwwroot/amazon-scraper-api`

2. **配置反向代理**
   - 点击网站设置 -> 反向代理
   - 添加反向代理:
     ```
     代理名称: amazon-api
     目标URL: http://127.0.0.1:8000
     发送域名: $host
     ```

3. **Nginx 配置示例**
   ```nginx
   location / {
       proxy_pass http://127.0.0.1:8000;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_connect_timeout 60s;
       proxy_send_timeout 60s;
       proxy_read_timeout 60s;
   }
   ```

## 常见问题解决

### 1. 启动失败

**问题**: 项目无法启动
**解决方案**:
```bash
# 检查日志
tail -f /www/wwwroot/amazon-scraper-api/logs/api.log

# 手动启动测试
cd /www/wwwroot/amazon-scraper-api
source venv/bin/activate
python start.py
```

### 2. 依赖安装失败

**问题**: requirements.txt 安装失败
**解决方案**:
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者逐个安装
pip install fastapi uvicorn pydantic requests beautifulsoup4
```

### 3. 端口被占用

**问题**: 端口 8000 被占用
**解决方案**:
1. 修改环境变量 PORT 为其他值 (如 8001)
2. 或者停止占用端口的进程:
```bash
# 查找占用进程
netstat -tlnp | grep :8000
# 停止进程
kill -9 进程ID
```

### 4. 权限问题

**问题**: 文件权限不足
**解决方案**:
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/amazon-scraper-api
chmod -R 755 /www/wwwroot/amazon-scraper-api
```

### 5. 内存不足

**问题**: 服务器内存不足
**解决方案**:
1. 减少 WORKERS 数量为 1
2. 优化代码减少内存使用
3. 增加服务器内存或使用 swap

## 监控和维护

### 日志监控
```bash
# 实时查看日志
tail -f /www/wwwroot/amazon-scraper-api/logs/api.log

# 查看错误日志
grep ERROR /www/wwwroot/amazon-scraper-api/logs/api.log
```

### 性能监控
- 在宝塔面板查看 CPU 和内存使用情况
- 监控 API 响应时间
- 定期检查日志文件大小

### 自动重启
在宝塔面板中可以设置项目自动重启：
1. 进入项目设置
2. 开启 "自动重启"
3. 设置重启条件 (如内存超限)

## 更新日志

### v1.0.0 (2025-08-15)
- 初始版本发布
- 支持 Amazon 产品信息提取
- 支持宝塔面板部署
- 完整的 API 文档
