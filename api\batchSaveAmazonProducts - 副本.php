<?php
/**
 * 超高速 Amazon 产品批量保存接口 - 性能优化版
 * 优化点：
 * 1. 数据库连接池优化
 * 2. 预处理语句防SQL注入
 * 3. 智能批量大小调整
 * 4. 性能监控和日志
 * 5. 错误处理增强
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 性能监控开始
$startTime = microtime(true);
$memoryStart = memory_get_usage();

// 加载数据库配置
$config = require_once dirname(__FILE__) . '/config.php';

/**
 * 创建优化的数据库连接
 */
function createOptimizedConnection($dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => false,
        // 性能优化参数
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET SESSION sql_mode='', innodb_lock_wait_timeout=10",
        PDO::ATTR_TIMEOUT => 30,
        PDO::ATTR_PERSISTENT => false, // 避免连接池问题
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
    ];

    return new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], $options);
}

try {
    $db = $config['db'];
    $pdo = createOptimizedConnection($db);
} catch (PDOException $e) {
    exit(json_encode([
        'success' => false,
        'message' => "数据库连接失败: " . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]));
}

/**
 * 数据验证和清理函数
 */
function validateAndCleanProduct($product) {
    if (empty($product['product_url'])) {
        return null;
    }

    // 处理 /dp/ 相对链接
    $url = trim($product['product_url']);
    if (strpos($url, '/dp/') === 0) {
        $url = 'https://www.amazon.com' . $url;
    }

    // 验证URL长度（数据库限制1000字符）
    if (strlen($url) > 1000) {
        return null;
    }

    return [
        'status' => $product['status'] ?? 'extracted',
        'product_url' => $url,
        'price' => is_numeric($product['price'] ?? null) ? (float)$product['price'] : null,
        'brand' => isset($product['brand']) ? substr(trim($product['brand']), 0, 100) : null,
        'title' => isset($product['title']) ? substr(trim($product['title']), 0, 500) : null,
        'sales' => isset($product['sales']) ? (int)$product['sales'] : 0,
        'extract_time' => $product['extract_time'] ?? date('Y-m-d H:i:s'),
        'warehouse_id' => isset($product['warehouse_id']) ? substr(trim($product['warehouse_id']), 0, 50) : null,
    ];
}

/**
 * 智能批量大小计算
 */
function calculateOptimalBatchSize($totalCount) {
    if ($totalCount <= 100) return 50;
    if ($totalCount <= 1000) return 200;
    if ($totalCount <= 5000) return 500;
    if ($totalCount <= 10000) return 1000;
    return 1500; // 最大批量，避免锁等待过长
}

// 读取 JSON 输入
$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['products']) || !is_array($input['products'])) {
    exit(json_encode([
        'success' => false,
        'message' => '请求数据格式错误',
        'timestamp' => date('Y-m-d H:i:s')
    ]));
}

$products = $input['products'];
$sourceUrl = $input['source_url'] ?? '';

// 数据预处理和验证
$validProducts = [];
$invalidCount = 0;

foreach ($products as $product) {
    $cleanProduct = validateAndCleanProduct($product);
    if ($cleanProduct !== null) {
        $validProducts[] = $cleanProduct;
    } else {
        $invalidCount++;
    }
}

$total = count($validProducts);
if ($total === 0) {
    exit(json_encode([
        'success' => false,
        'message' => '没有有效的产品数据',
        'invalid_count' => $invalidCount,
        'timestamp' => date('Y-m-d H:i:s')
    ]));
}

// 智能批量大小
$batchSize = calculateOptimalBatchSize($total);

$inserted = 0;
$duplicates = 0;
$errors = [];

// 开启事务
$pdo->beginTransaction();

try {
    // 准备预处理语句（安全且高效）
    $sql = "INSERT IGNORE INTO amazon_products
            (status, product_url, price, brand, title, sales, extract_time, warehouse_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    $stmt = $pdo->prepare($sql);

    $chunks = array_chunk($validProducts, $batchSize);
    $batchCount = 0;

    foreach ($chunks as $chunkIndex => $chunk) {
        $batchStartTime = microtime(true);
        $batchInserted = 0;

        foreach ($chunk as $product) {
            try {
                $stmt->execute([
                    $product['status'],
                    $product['product_url'],
                    $product['price'],
                    $product['brand'],
                    $product['title'],
                    $product['sales'],
                    $product['extract_time'],
                    $product['warehouse_id']
                ]);

                if ($stmt->rowCount() > 0) {
                    $batchInserted++;
                    $inserted++;
                } else {
                    $duplicates++;
                }
            } catch (PDOException $e) {
                // 记录单条错误，但继续处理其他数据
                $errors[] = [
                    'product_url' => $product['product_url'],
                    'error' => $e->getMessage()
                ];

                // 如果错误过多，停止处理
                if (count($errors) > 100) {
                    throw new Exception("错误过多，停止处理");
                }
            }
        }

        $batchCount++;
        $batchTime = microtime(true) - $batchStartTime;

        // 记录批次处理信息（可选）
        if ($batchTime > 2.0) { // 如果单批次超过2秒，记录警告
            error_log("Batch {$batchCount} took {$batchTime}s, inserted {$batchInserted} records");
        }
    }

    $pdo->commit();

} catch (Exception $e) {
    $pdo->rollBack();
    exit(json_encode([
        'success' => false,
        'message' => '批量保存失败: ' . $e->getMessage(),
        'processed_batches' => $batchCount ?? 0,
        'inserted_before_error' => $inserted,
        'errors' => array_slice($errors, 0, 10), // 只返回前10个错误
        'timestamp' => date('Y-m-d H:i:s')
    ]));
}

// 计算性能指标
$endTime = microtime(true);
$executionTime = $endTime - $startTime;
$memoryUsed = memory_get_usage() - $memoryStart;
$peakMemory = memory_get_peak_usage();

$response = [
    'success' => true,
    'message' => '超高速批量保存完成 - 性能优化版',
    'summary' => [
        'total_submitted' => count($input['products']),
        'valid_products' => $total,
        'invalid_products' => $invalidCount,
        'inserted_count' => $inserted,
        'duplicate_count' => $duplicates,
        'error_count' => count($errors),
        'source_url' => $sourceUrl
    ],
    'performance' => [
        'execution_time_ms' => round($executionTime * 1000, 2),
        'memory_used_mb' => round($memoryUsed / 1024 / 1024, 2),
        'peak_memory_mb' => round($peakMemory / 1024 / 1024, 2),
        'records_per_second' => $executionTime > 0 ? round($total / $executionTime, 0) : 0,
        'batch_size_used' => $batchSize,
        'total_batches' => $batchCount ?? 0
    ],
    'optimization_info' => [
        'connection_optimized' => true,
        'prepared_statements' => true,
        'smart_batch_sizing' => true,
        'data_validation' => true,
        'error_handling' => true
    ]
];

// 如果有错误，包含错误信息（但不影响成功状态）
if (!empty($errors)) {
    $response['warnings'] = [
        'message' => '部分记录处理时出现错误',
        'error_samples' => array_slice($errors, 0, 5) // 只显示前5个错误样本
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
