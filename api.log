[2025-08-17 07:28:05] 📥 收到 GET 请求
[2025-08-17 07:28:05] 🔍 检查环境...
[2025-08-17 07:28:05] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 07:28:05] ✅ 参考.py: 存在
[2025-08-17 07:28:05] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 07:28:05] ✅ base64.txt: 存在
[2025-08-17 07:28:05] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 07:28:40] 🔍 检查环境...
[2025-08-17 07:28:40] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 07:28:40] ✅ 参考.py: 存在
[2025-08-17 07:28:40] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 07:28:40] ✅ base64.txt: 存在
[2025-08-17 07:28:40] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:26] 📥 收到 GET 请求
[2025-08-17 13:23:26] 🔍 检查环境...
[2025-08-17 13:23:26] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:26] ✅ 参考.py: 存在
[2025-08-17 13:23:26] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:26] ✅ base64.txt: 存在
[2025-08-17 13:23:26] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:28] 📥 收到 GET 请求
[2025-08-17 13:23:28] 🔍 检查环境...
[2025-08-17 13:23:28] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:28] ✅ 参考.py: 存在
[2025-08-17 13:23:28] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:28] ✅ base64.txt: 存在
[2025-08-17 13:23:28] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:29] 📥 收到 GET 请求
[2025-08-17 13:23:29] 🔍 检查环境...
[2025-08-17 13:23:29] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:29] ✅ 参考.py: 存在
[2025-08-17 13:23:29] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:29] ✅ base64.txt: 存在
[2025-08-17 13:23:29] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:29] 📥 收到 GET 请求
[2025-08-17 13:23:29] 🔍 检查环境...
[2025-08-17 13:23:29] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:29] ✅ 参考.py: 存在
[2025-08-17 13:23:29] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:29] ✅ base64.txt: 存在
[2025-08-17 13:23:29] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:29] 📥 收到 GET 请求
[2025-08-17 13:23:29] 🔍 检查环境...
[2025-08-17 13:23:29] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:29] ✅ 参考.py: 存在
[2025-08-17 13:23:29] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:29] ✅ base64.txt: 存在
[2025-08-17 13:23:29] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:30] 📥 收到 GET 请求
[2025-08-17 13:23:30] 🔍 检查环境...
[2025-08-17 13:23:30] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:30] ✅ 参考.py: 存在
[2025-08-17 13:23:30] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:30] ✅ base64.txt: 存在
[2025-08-17 13:23:30] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:30] 📥 收到 GET 请求
[2025-08-17 13:23:30] 🔍 检查环境...
[2025-08-17 13:23:30] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:30] ✅ 参考.py: 存在
[2025-08-17 13:23:30] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:30] ✅ base64.txt: 存在
[2025-08-17 13:23:30] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:30] 📥 收到 GET 请求
[2025-08-17 13:23:30] 🔍 检查环境...
[2025-08-17 13:23:30] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:30] ✅ 参考.py: 存在
[2025-08-17 13:23:30] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:30] ✅ base64.txt: 存在
[2025-08-17 13:23:30] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:30] 📥 收到 GET 请求
[2025-08-17 13:23:30] 🔍 检查环境...
[2025-08-17 13:23:30] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:30] ✅ 参考.py: 存在
[2025-08-17 13:23:30] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:30] ✅ base64.txt: 存在
[2025-08-17 13:23:30] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:30] 📥 收到 GET 请求
[2025-08-17 13:23:30] 🔍 检查环境...
[2025-08-17 13:23:30] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:30] ✅ 参考.py: 存在
[2025-08-17 13:23:30] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:30] ✅ base64.txt: 存在
[2025-08-17 13:23:30] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:31] 📥 收到 GET 请求
[2025-08-17 13:23:31] 🔍 检查环境...
[2025-08-17 13:23:31] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:31] ✅ 参考.py: 存在
[2025-08-17 13:23:31] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:31] ✅ base64.txt: 存在
[2025-08-17 13:23:31] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:31] 📥 收到 GET 请求
[2025-08-17 13:23:31] 🔍 检查环境...
[2025-08-17 13:23:31] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:31] ✅ 参考.py: 存在
[2025-08-17 13:23:31] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:31] ✅ base64.txt: 存在
[2025-08-17 13:23:31] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:31] 📥 收到 GET 请求
[2025-08-17 13:23:31] 🔍 检查环境...
[2025-08-17 13:23:31] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:31] ✅ 参考.py: 存在
[2025-08-17 13:23:31] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:31] ✅ base64.txt: 存在
[2025-08-17 13:23:31] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:31] 📥 收到 GET 请求
[2025-08-17 13:23:31] 🔍 检查环境...
[2025-08-17 13:23:31] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:31] ✅ 参考.py: 存在
[2025-08-17 13:23:31] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:31] ✅ base64.txt: 存在
[2025-08-17 13:23:31] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
[2025-08-17 13:23:31] 📥 收到 GET 请求
[2025-08-17 13:23:31] 🔍 检查环境...
[2025-08-17 13:23:31] 📁 当前目录: /www/wwwroot/192.168.110.35/amaon/ifyapi
[2025-08-17 13:23:31] ✅ 参考.py: 存在
[2025-08-17 13:23:31] ✅ 参考_简单域名修复版.py: 存在
[2025-08-17 13:23:31] ✅ base64.txt: 存在
[2025-08-17 13:23:31] 🐍 Python版本: 3.11.9 (main, Apr 23 2025, 08:56:06) [GCC 11.4.0]
