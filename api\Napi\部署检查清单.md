# Amazon Scraper API 部署检查清单

## 📋 部署前检查

### 1. 环境要求检查

- [ ] **服务器配置**
  - [ ] CPU: 1核以上
  - [ ] 内存: 1GB以上
  - [ ] 磁盘: 10GB以上可用空间
  - [ ] 网络: 稳定的互联网连接

- [ ] **宝塔面板**
  - [ ] 宝塔面板版本 7.0+
  - [ ] Python 项目管理器已安装
  - [ ] Python 3.8+ 版本可用

- [ ] **系统权限**
  - [ ] 具有 root 或管理员权限
  - [ ] 可以创建和修改文件
  - [ ] 可以安装 Python 包

### 2. 文件准备检查

- [ ] **核心文件**
  - [ ] `main.py` - FastAPI 主应用 ✅
  - [ ] `amazon_scraper.py` - 爬虫核心逻辑 ✅
  - [ ] `scraper_adapter.py` - 适配器 ✅
  - [ ] `start.py` - 启动脚本 ✅
  - [ ] `requirements.txt` - 依赖列表 ✅

- [ ] **配置文件**
  - [ ] `gunicorn.conf.py` - 生产环境配置 ✅
  - [ ] `bt_config.json` - 宝塔配置 ✅
  - [ ] `run.sh` / `run.bat` - 启动脚本 ✅

- [ ] **文档文件**
  - [ ] `README.md` - 项目文档 ✅
  - [ ] `宝塔面板配置指南.md` - 配置指南 ✅
  - [ ] `部署检查清单.md` - 本文件 ✅

- [ ] **测试文件**
  - [ ] `test_api.py` - API 测试脚本 ✅

## 🚀 部署步骤检查

### 步骤 1: 创建项目

- [ ] 在宝塔面板中创建 Python 项目
- [ ] 项目名称: `amazon-scraper-api`
- [ ] 项目路径: `/www/wwwroot/amazon-scraper-api`
- [ ] Python 版本: 3.8+
- [ ] 启动文件: `start.py`
- [ ] 端口: `8000`

### 步骤 2: 上传文件

- [ ] 所有核心文件已上传到项目目录
- [ ] 文件权限设置正确 (755)
- [ ] 文件所有者设置为 www:www

### 步骤 3: 安装依赖

- [ ] 通过宝塔面板安装 requirements.txt
- [ ] 或手动安装所有依赖包
- [ ] 验证关键依赖包已安装:
  - [ ] fastapi
  - [ ] uvicorn
  - [ ] pydantic
  - [ ] requests
  - [ ] beautifulsoup4

### 步骤 4: 环境变量配置

- [ ] HOST = 0.0.0.0
- [ ] PORT = 8000
- [ ] WORKERS = 1
- [ ] LOG_LEVEL = info

### 步骤 5: 启动项目

- [ ] 在宝塔面板中启动项目
- [ ] 项目状态显示 "运行中"
- [ ] 查看日志无错误信息

## 🧪 功能测试检查

### 基础功能测试

- [ ] **健康检查**
  ```bash
  curl http://localhost:8000/api/health
  ```
  预期结果: 返回 200 状态码和健康状态信息

- [ ] **API 文档访问**
  ```bash
  curl http://localhost:8000/api/docs
  ```
  预期结果: 返回 Swagger UI 页面

- [ ] **根路径访问**
  ```bash
  curl http://localhost:8000/
  ```
  预期结果: 返回服务信息

### 核心功能测试

- [ ] **HTML 验证功能**
  - [ ] 使用 test_api.py 脚本测试
  - [ ] 手动测试 /api/validate-html 端点

- [ ] **产品信息提取功能**
  - [ ] 使用测试 HTML 内容
  - [ ] 验证返回的产品信息格式
  - [ ] 检查处理时间是否合理

- [ ] **统计信息功能**
  - [ ] 访问 /api/stats 端点
  - [ ] 验证返回的统计数据

### 性能测试

- [ ] **响应时间测试**
  - [ ] 健康检查 < 1秒
  - [ ] 产品信息提取 < 30秒
  - [ ] API 文档加载 < 5秒

- [ ] **并发测试**
  - [ ] 多个并发请求处理正常
  - [ ] 服务器资源使用合理

## 🔧 生产环境配置检查

### 安全配置

- [ ] **防火墙设置**
  - [ ] 只开放必要端口
  - [ ] 限制访问来源 IP (如需要)

- [ ] **SSL 证书** (如使用域名)
  - [ ] 申请并安装 SSL 证书
  - [ ] 强制 HTTPS 重定向

- [ ] **访问控制**
  - [ ] 考虑添加 API 密钥验证
  - [ ] 设置请求频率限制

### 监控配置

- [ ] **日志配置**
  - [ ] 日志文件正常生成
  - [ ] 日志轮转配置
  - [ ] 错误日志监控

- [ ] **性能监控**
  - [ ] CPU 使用率监控
  - [ ] 内存使用率监控
  - [ ] 磁盘空间监控

- [ ] **服务监控**
  - [ ] 服务状态监控
  - [ ] 自动重启配置
  - [ ] 告警通知设置

### 备份配置

- [ ] **代码备份**
  - [ ] 定期备份项目文件
  - [ ] 版本控制 (Git)

- [ ] **配置备份**
  - [ ] 备份环境变量配置
  - [ ] 备份 Nginx 配置

- [ ] **日志备份**
  - [ ] 定期备份重要日志
  - [ ] 日志文件压缩存储

## 🚨 故障排除检查

### 常见问题检查

- [ ] **启动失败**
  - [ ] 检查 Python 版本兼容性
  - [ ] 检查依赖包安装状态
  - [ ] 检查端口占用情况
  - [ ] 查看详细错误日志

- [ ] **性能问题**
  - [ ] 检查服务器资源使用
  - [ ] 调整工作进程数量
  - [ ] 优化代码性能

- [ ] **网络问题**
  - [ ] 检查防火墙设置
  - [ ] 验证网络连通性
  - [ ] 检查代理配置

### 日志检查

- [ ] **应用日志**
  ```bash
  tail -f /www/wwwroot/amazon-scraper-api/logs/api.log
  ```

- [ ] **错误日志**
  ```bash
  grep ERROR /www/wwwroot/amazon-scraper-api/logs/api.log
  ```

- [ ] **访问日志**
  ```bash
  tail -f /www/wwwroot/amazon-scraper-api/logs/access.log
  ```

## ✅ 部署完成确认

### 最终验证

- [ ] 所有测试用例通过
- [ ] API 文档可正常访问
- [ ] 服务运行稳定
- [ ] 日志记录正常
- [ ] 性能指标正常

### 交付文档

- [ ] 提供 API 使用文档
- [ ] 提供故障排除指南
- [ ] 提供联系方式和支持信息

## 📞 支持信息

**技术支持**:
- 查看项目 README.md 文件
- 检查宝塔面板配置指南
- 运行 test_api.py 进行诊断

**常用命令**:
```bash
# 查看服务状态
systemctl status amazon-scraper-api

# 重启服务
systemctl restart amazon-scraper-api

# 查看实时日志
tail -f /www/wwwroot/amazon-scraper-api/logs/api.log

# 测试 API 功能
python test_api.py
```

---

**部署完成日期**: ___________  
**部署人员**: ___________  
**验证人员**: ___________
