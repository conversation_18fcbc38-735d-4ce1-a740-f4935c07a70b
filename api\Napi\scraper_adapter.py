#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Amazon Scraper Adapter for API Service
适配器模块，用于将现有的 amazon_scraper.py 适配为 API 服务
"""

import logging
import traceback
from typing import Dict, Any, Optional
from amazon_scraper import AmazonProductScraper, get_headers_with_currency

logger = logging.getLogger(__name__)

class ApiAmazonScraper:
    """
    Amazon 爬虫 API 适配器
    封装原有的 AmazonProductScraper 类，提供更适合 API 服务的接口
    """
    
    def __init__(self, html_content: str, verbose: bool = False):
        """
        初始化适配器
        
        :param html_content: HTML 内容
        :param verbose: 是否显示详细日志
        """
        self.html_content = html_content
        self.verbose = verbose
        self.scraper = None
        self.product_data = {}
        
        # 初始化爬虫实例
        try:
            self.scraper = AmazonProductScraper(
                html_content=html_content,
                verbose=verbose
            )
            logger.info(f"Amazon 爬虫适配器初始化成功，HTML 长度: {len(html_content)}")
        except Exception as e:
            logger.error(f"初始化 Amazon 爬虫适配器失败: {str(e)}")
            raise
    
    def extract_product_info(self) -> bool:
        """
        提取产品信息
        
        :return: 是否成功提取
        """
        try:
            if not self.scraper:
                logger.error("爬虫实例未初始化")
                return False
            
            # 加载 HTML
            if not self.scraper.load_html():
                logger.error("加载 HTML 内容失败")
                return False
            
            # 提取产品信息
            if not self.scraper.extract_product_info():
                logger.error("提取产品信息失败")
                return False
            
            # 保存产品数据
            self.product_data = self.scraper.product_data.copy()
            
            logger.info("产品信息提取成功")
            return True
            
        except Exception as e:
            logger.error(f"提取产品信息时出错: {str(e)}")
            if self.verbose:
                traceback.print_exc()
            return False
    
    def get_product_data(self) -> Dict[str, Any]:
        """
        获取提取的产品数据
        
        :return: 产品数据字典
        """
        return self.product_data.copy()
    
    def send_to_api(self, api_url: str, warehouse_id: str, source_link: str = "") -> Dict[str, Any]:
        """
        发送产品数据到 API
        
        :param api_url: API 接口地址
        :param warehouse_id: 仓库 ID
        :param source_link: 产品来源链接
        :return: API 发送结果
        """
        try:
            if not self.scraper:
                logger.error("爬虫实例未初始化")
                return {
                    "success": False,
                    "error": "爬虫实例未初始化",
                    "status_code": None,
                    "response": None
                }
            
            logger.info(f"发送产品数据到 API: {api_url}")
            
            # 调用原有的 send_to_api 方法
            result = self.scraper.send_to_api(
                api_url=api_url,
                warehouse_id=warehouse_id,
                source_link=source_link
            )
            
            logger.info(f"API 发送结果: {'成功' if result.get('success', False) else '失败'}")
            return result
            
        except Exception as e:
            logger.error(f"发送到 API 时出错: {str(e)}")
            if self.verbose:
                traceback.print_exc()
            return {
                "success": False,
                "error": f"发送到 API 时出错: {str(e)}",
                "status_code": None,
                "response": str(e)
            }
    
    def validate_product_data(self) -> Dict[str, Any]:
        """
        验证产品数据的完整性
        
        :return: 验证结果
        """
        validation_result = {
            "is_valid": True,
            "missing_fields": [],
            "warnings": []
        }
        
        # 必需字段检查
        required_fields = {
            "标题": "产品标题",
            "SKU": "产品 SKU"
        }
        
        for field, description in required_fields.items():
            if not self.product_data.get(field):
                validation_result["is_valid"] = False
                validation_result["missing_fields"].append({
                    "field": field,
                    "description": description
                })
        
        # 可选但重要的字段检查
        important_fields = {
            "价格": "产品价格",
            "产品图片": "产品图片",
            "描述": "产品描述"
        }
        
        for field, description in important_fields.items():
            if not self.product_data.get(field):
                validation_result["warnings"].append({
                    "field": field,
                    "description": description,
                    "message": f"缺少{description}信息"
                })
        
        # 特殊验证
        if self.product_data.get("产品图片") and len(self.product_data["产品图片"]) == 0:
            validation_result["warnings"].append({
                "field": "产品图片",
                "description": "产品图片",
                "message": "产品图片列表为空"
            })
        
        return validation_result
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取产品信息摘要
        
        :return: 产品信息摘要
        """
        if not self.product_data:
            return {"error": "没有可用的产品数据"}
        
        summary = {
            "title": self.product_data.get("标题", ""),
            "sku": self.product_data.get("SKU", ""),
            "price": self.product_data.get("价格", ""),
            "image_count": len(self.product_data.get("产品图片", [])),
            "variant_count": len(self.product_data.get("产品变体", [])),
            "has_description": bool(self.product_data.get("描述", "")),
            "has_video": bool(self.product_data.get("视频链接", "")),
            "rating": self.product_data.get("评分", ""),
            "review_count": self.product_data.get("评论数", ""),
            "categories": self.product_data.get("产品分类", "")
        }
        
        return summary

def create_scraper(html_content: str, verbose: bool = False) -> ApiAmazonScraper:
    """
    创建 Amazon 爬虫适配器实例
    
    :param html_content: HTML 内容
    :param verbose: 是否显示详细日志
    :return: 爬虫适配器实例
    """
    return ApiAmazonScraper(html_content=html_content, verbose=verbose)

def validate_html_content(html_content: str) -> Dict[str, Any]:
    """
    验证 HTML 内容是否适合处理
    
    :param html_content: HTML 内容
    :return: 验证结果
    """
    validation_result = {
        "is_valid": True,
        "errors": [],
        "warnings": []
    }
    
    # 基本长度检查
    if len(html_content) < 1000:
        validation_result["is_valid"] = False
        validation_result["errors"].append("HTML 内容过短，可能不是完整的商品页面")
    
    # Amazon 页面标识检查
    amazon_indicators = [
        "amazon",
        "productTitle",
        "asin",
        "twister"
    ]
    
    found_indicators = []
    for indicator in amazon_indicators:
        if indicator.lower() in html_content.lower():
            found_indicators.append(indicator)
    
    if len(found_indicators) < 2:
        validation_result["is_valid"] = False
        validation_result["errors"].append(
            f"HTML 内容缺少 Amazon 页面标识，只找到: {found_indicators}"
        )
    
    # 检查是否是错误页面
    error_indicators = [
        "Amazon CAPTCHA",
        "Bot Check",
        "Access Denied",
        "Page Not Found",
        "404"
    ]
    
    for error_indicator in error_indicators:
        if error_indicator in html_content:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"检测到错误页面标识: {error_indicator}")
    
    # 检查页面完整性
    if "productTitle" not in html_content:
        validation_result["warnings"].append("未找到产品标题元素，可能影响信息提取")
    
    if "priceblock" not in html_content and "price" not in html_content.lower():
        validation_result["warnings"].append("未找到价格相关元素，可能影响价格提取")
    
    return validation_result
