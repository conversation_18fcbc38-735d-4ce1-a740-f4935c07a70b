#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Amazon Scraper Web API Service
基于 FastAPI 的亚马逊产品信息提取 Web API 服务
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import uvicorn

# 导入现有的 Amazon 爬虫类和适配器
from amazon_scraper import AmazonProductScraper, get_headers_with_currency
from scraper_adapter import ApiAmazonScraper, create_scraper, validate_html_content

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用启动和关闭时的生命周期管理"""
    # 启动时执行
    logger.info("Amazon Scraper API 服务启动")
    yield
    # 关闭时执行
    logger.info("Amazon Scraper API 服务关闭")

# 创建 FastAPI 应用
app = FastAPI(
    title="Amazon Scraper API",
    description="亚马逊产品信息提取 Web API 服务",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有HTTP请求"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"请求开始: {request.method} {request.url}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.2f}秒")
    
    return response

# 数据模型定义
class ScrapeRequest(BaseModel):
    """爬取请求数据模型"""
    html_content: str = Field(..., description="Amazon 商品页面的 HTML 内容", min_length=100)
    warehouse_id: str = Field(default="3079", description="仓库 ID")
    api_url: Optional[str] = Field(default="http://**************/wms/product/importProducts", description="API 接口地址")
    currency: str = Field(default="EUR", description="货币类型")
    source_link: Optional[str] = Field(default="", description="产品来源链接")
    send_to_api: bool = Field(default=True, description="是否发送到 API 接口")
    verbose: bool = Field(default=False, description="是否显示详细日志")
    
    @validator('html_content')
    def validate_html_content(cls, v):
        """验证 HTML 内容"""
        if not v or len(v.strip()) < 100:
            raise ValueError('HTML 内容不能为空且长度至少为 100 字符')
        
        # 检查是否包含 Amazon 相关标识
        amazon_indicators = ['amazon', 'productTitle', 'asin', 'twister']
        if not any(indicator.lower() in v.lower() for indicator in amazon_indicators):
            raise ValueError('HTML 内容似乎不是来自 Amazon 商品页面')
        
        return v
    
    @validator('warehouse_id')
    def validate_warehouse_id(cls, v):
        """验证仓库 ID"""
        if not v or not v.strip():
            raise ValueError('仓库 ID 不能为空')
        return v.strip()

class ProductInfo(BaseModel):
    """产品信息数据模型"""
    title: str = Field(description="产品标题")
    description: str = Field(description="产品描述")
    short_description: str = Field(description="短描述")
    price: str = Field(description="产品价格")
    sku: str = Field(description="产品 SKU")
    images: List[str] = Field(description="产品图片链接列表")
    categories: str = Field(description="产品分类")
    variants: List[Dict[str, Any]] = Field(description="产品变体信息")
    rating: str = Field(description="产品评分")
    review_count: str = Field(description="评论数量")
    sales_info: str = Field(description="销量信息")
    video_url: str = Field(description="视频链接")
    brand: str = Field(description="品牌信息")

class ApiResult(BaseModel):
    """API 发送结果数据模型"""
    success: bool = Field(description="是否成功")
    status_code: Optional[int] = Field(description="HTTP 状态码")
    error: Optional[str] = Field(description="错误信息")
    response: Optional[str] = Field(description="响应内容")

class ScrapeResponse(BaseModel):
    """爬取响应数据模型"""
    success: bool = Field(description="处理是否成功")
    message: str = Field(description="处理消息")
    product_info: Optional[ProductInfo] = Field(description="提取的产品信息")
    api_result: Optional[ApiResult] = Field(description="API 发送结果")
    processing_time: float = Field(description="处理耗时（秒）")
    timestamp: str = Field(description="处理时间戳")

class ErrorResponse(BaseModel):
    """错误响应数据模型"""
    success: bool = Field(default=False, description="处理是否成功")
    error: str = Field(description="错误信息")
    detail: Optional[str] = Field(description="详细错误信息")
    timestamp: str = Field(description="错误时间戳")

# 健康检查端点
@app.get("/api/health", summary="健康检查", description="检查 API 服务状态")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "Amazon Scraper API",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

# 根路径重定向到文档
@app.get("/", summary="根路径", description="重定向到 API 文档")
async def root():
    """根路径，重定向到 API 文档"""
    return {
        "message": "Amazon Scraper API 服务正在运行",
        "docs": "/api/docs",
        "health": "/api/health"
    }

# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP 异常处理器"""
    logger.error(f"HTTP 异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"HTTP {exc.status_code}",
            detail=str(exc.detail),
            timestamp=datetime.now().isoformat()
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="内部服务器错误",
            detail=str(exc),
            timestamp=datetime.now().isoformat()
        ).dict()
    )

# 辅助函数
def convert_product_data_to_model(product_data: Dict[str, Any]) -> ProductInfo:
    """将爬虫提取的产品数据转换为 ProductInfo 模型"""
    return ProductInfo(
        title=product_data.get('标题', ''),
        description=product_data.get('描述', ''),
        short_description=product_data.get('短描述', ''),
        price=product_data.get('价格', ''),
        sku=product_data.get('SKU', ''),
        images=product_data.get('产品图片', []),
        categories=product_data.get('产品分类', ''),
        variants=product_data.get('产品变体', []),
        rating=product_data.get('评分', ''),
        review_count=product_data.get('评论数', ''),
        sales_info=product_data.get('销量信息', ''),
        video_url=product_data.get('视频链接', ''),
        brand=product_data.get('品牌', '')
    )

def convert_api_result_to_model(api_result: Dict[str, Any]) -> ApiResult:
    """将 API 发送结果转换为 ApiResult 模型"""
    return ApiResult(
        success=api_result.get('success', False),
        status_code=api_result.get('status_code'),
        error=api_result.get('error'),
        response=api_result.get('response')
    )

# 主要的爬取端点
@app.post("/api/scrape",
          response_model=ScrapeResponse,
          summary="提取 Amazon 产品信息",
          description="接收 HTML 内容并提取 Amazon 产品信息，可选择发送到指定 API")
async def scrape_amazon_product(request: ScrapeRequest, background_tasks: BackgroundTasks):
    """
    提取 Amazon 产品信息的主要端点

    - **html_content**: Amazon 商品页面的 HTML 内容
    - **warehouse_id**: 仓库 ID（默认: 3079）
    - **api_url**: API 接口地址（可选）
    - **currency**: 货币类型（默认: EUR）
    - **source_link**: 产品来源链接（可选）
    - **send_to_api**: 是否发送到 API 接口（默认: True）
    - **verbose**: 是否显示详细日志（默认: False）
    """
    start_time = time.time()

    try:
        logger.info(f"开始处理产品信息提取请求 - 仓库ID: {request.warehouse_id}")

        # 验证 HTML 内容
        html_validation = validate_html_content(request.html_content)
        if not html_validation["is_valid"]:
            error_msg = "; ".join(html_validation["errors"])
            raise HTTPException(
                status_code=400,
                detail=f"HTML 内容验证失败: {error_msg}"
            )

        # 记录警告信息
        if html_validation["warnings"]:
            for warning in html_validation["warnings"]:
                logger.warning(f"HTML 内容警告: {warning}")

        # 创建 Amazon 产品爬虫适配器实例
        scraper = create_scraper(
            html_content=request.html_content,
            verbose=request.verbose
        )

        # 提取产品信息
        if not scraper.extract_product_info():
            raise HTTPException(
                status_code=400,
                detail="无法从 HTML 内容中提取产品信息，可能页面格式不正确或内容不完整"
            )

        # 验证产品数据
        validation_result = scraper.validate_product_data()
        if not validation_result["is_valid"]:
            missing_fields = [f["description"] for f in validation_result["missing_fields"]]
            raise HTTPException(
                status_code=400,
                detail=f"产品数据不完整，缺少必需字段: {', '.join(missing_fields)}"
            )

        # 记录警告信息
        if validation_result["warnings"]:
            for warning in validation_result["warnings"]:
                logger.warning(f"产品数据警告: {warning['message']}")

        # 转换产品数据为响应模型
        product_data = scraper.get_product_data()
        product_info = convert_product_data_to_model(product_data)

        # 检查必要信息
        if not product_info.title:
            raise HTTPException(
                status_code=400,
                detail="未能提取到产品标题，页面可能受限或格式异常"
            )

        # 发送到 API（如果需要）
        api_result = None
        if request.send_to_api and request.api_url:
            try:
                logger.info(f"发送产品信息到 API: {request.api_url}")
                api_response = scraper.send_to_api(
                    api_url=request.api_url,
                    warehouse_id=request.warehouse_id,
                    source_link=request.source_link
                )
                api_result = convert_api_result_to_model(api_response)

                if api_result.success:
                    logger.info("产品信息成功发送到 API")
                else:
                    logger.warning(f"API 发送失败: {api_result.error}")

            except Exception as e:
                logger.error(f"发送到 API 时出错: {str(e)}")
                api_result = ApiResult(
                    success=False,
                    error=f"发送到 API 时出错: {str(e)}",
                    status_code=None,
                    response=None
                )

        # 计算处理时间
        processing_time = time.time() - start_time

        # 构建响应
        response = ScrapeResponse(
            success=True,
            message="产品信息提取成功",
            product_info=product_info,
            api_result=api_result,
            processing_time=round(processing_time, 2),
            timestamp=datetime.now().isoformat()
        )

        logger.info(f"产品信息提取完成 - 耗时: {processing_time:.2f}秒")
        return response

    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        logger.error(f"处理请求时出现未知错误: {str(e)}")
        processing_time = time.time() - start_time

        raise HTTPException(
            status_code=500,
            detail=f"处理请求时出现内部错误: {str(e)}"
        )

# 批量处理端点（可选功能）
@app.post("/api/batch-scrape",
          summary="批量提取 Amazon 产品信息",
          description="批量处理多个 HTML 内容")
async def batch_scrape_amazon_products(
    requests: List[ScrapeRequest],
    background_tasks: BackgroundTasks
):
    """
    批量提取 Amazon 产品信息

    接收多个 ScrapeRequest 并并行处理
    """
    if len(requests) > 10:  # 限制批量处理数量
        raise HTTPException(
            status_code=400,
            detail="批量处理最多支持 10 个请求"
        )

    start_time = time.time()
    results = []

    logger.info(f"开始批量处理 {len(requests)} 个产品信息提取请求")

    for i, req in enumerate(requests):
        try:
            logger.info(f"处理第 {i+1}/{len(requests)} 个请求")

            # 为每个请求调用单个处理函数
            result = await scrape_amazon_product(req, background_tasks)
            results.append({
                "index": i,
                "success": True,
                "result": result
            })

        except Exception as e:
            logger.error(f"处理第 {i+1} 个请求时出错: {str(e)}")
            results.append({
                "index": i,
                "success": False,
                "error": str(e)
            })

    processing_time = time.time() - start_time

    # 统计结果
    success_count = sum(1 for r in results if r["success"])

    logger.info(f"批量处理完成 - 成功: {success_count}/{len(requests)} - 耗时: {processing_time:.2f}秒")

    return {
        "success": True,
        "message": f"批量处理完成，成功处理 {success_count}/{len(requests)} 个请求",
        "results": results,
        "processing_time": round(processing_time, 2),
        "timestamp": datetime.now().isoformat()
    }

# 验证 HTML 内容端点
@app.post("/api/validate-html", summary="验证 HTML 内容", description="验证 HTML 内容是否适合处理")
async def validate_html(html_content: str = Field(..., description="要验证的 HTML 内容")):
    """验证 HTML 内容是否适合处理"""
    try:
        validation_result = validate_html_content(html_content)

        return {
            "success": True,
            "validation_result": validation_result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"验证 HTML 内容时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证 HTML 内容失败: {str(e)}")

# 获取服务统计信息
@app.get("/api/stats", summary="服务统计", description="获取 API 服务统计信息")
async def get_service_stats():
    """获取服务统计信息"""
    try:
        # 读取日志文件获取统计信息
        log_file = "api.log"
        stats = {
            "service": "Amazon Scraper API",
            "version": "1.0.0",
            "uptime": "运行中",
            "timestamp": datetime.now().isoformat()
        }

        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    stats["log_lines"] = len(lines)

                    # 统计请求数量
                    request_lines = [line for line in lines if "请求开始:" in line]
                    stats["total_requests"] = len(request_lines)

                    # 统计成功请求
                    success_lines = [line for line in lines if "产品信息提取完成" in line]
                    stats["successful_extractions"] = len(success_lines)

            except Exception as e:
                logger.warning(f"读取日志文件时出错: {e}")
                stats["log_error"] = str(e)

        return stats

    except Exception as e:
        logger.error(f"获取统计信息时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# 应用启动函数
def create_app() -> FastAPI:
    """创建并配置 FastAPI 应用"""
    return app

# 主函数
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Amazon Scraper API 服务')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--reload', action='store_true', help='开发模式，自动重载')
    parser.add_argument('--workers', type=int, default=1, help='工作进程数量')
    parser.add_argument('--log-level', type=str, default='info', help='日志级别')

    args = parser.parse_args()

    # 启动服务器
    logger.info(f"启动 Amazon Scraper API 服务 - {args.host}:{args.port}")

    uvicorn.run(
        "main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level=args.log_level,
        access_log=True
    )
