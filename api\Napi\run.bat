@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM Amazon Scraper API 启动脚本 (Windows)
REM 适用于宝塔面板 Windows 环境

echo ================================
echo Amazon Scraper API 启动脚本
echo ================================

REM 检查 Python
echo [INFO] 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 未找到 Python 解释器
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python 版本: %PYTHON_VERSION%

REM 创建必要目录
echo [INFO] 创建必要目录...
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp

REM 检查必要文件
echo [INFO] 检查必要文件...
if not exist "main.py" (
    echo [ERROR] 未找到 main.py 文件
    pause
    exit /b 1
)

if not exist "amazon_scraper.py" (
    echo [ERROR] 未找到 amazon_scraper.py 文件
    pause
    exit /b 1
)

if not exist "scraper_adapter.py" (
    echo [ERROR] 未找到 scraper_adapter.py 文件
    pause
    exit /b 1
)

REM 安装依赖
echo [INFO] 安装 Python 依赖...
if exist "requirements.txt" (
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo [WARN] 依赖安装可能有问题，但继续启动...
    ) else (
        echo [INFO] 依赖安装完成
    )
) else (
    echo [WARN] 未找到 requirements.txt 文件
)

REM 设置环境变量
if "%HOST%"=="" set HOST=0.0.0.0
if "%PORT%"=="" set PORT=8000
if "%WORKERS%"=="" set WORKERS=1
if "%LOG_LEVEL%"=="" set LOG_LEVEL=info

echo [INFO] 服务配置:
echo [INFO]   主机: %HOST%
echo [INFO]   端口: %PORT%
echo [INFO]   工作进程: %WORKERS%
echo [INFO]   日志级别: %LOG_LEVEL%

REM 启动服务
echo [INFO] 启动 Amazon Scraper API 服务...
python start.py

pause
