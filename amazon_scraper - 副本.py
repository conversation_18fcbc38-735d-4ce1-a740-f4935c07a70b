#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import sys
import json
import time
import random
import requests
from bs4 import BeautifulSoup, Tag, Comment
import traceback
from urllib.parse import unquote
from abc import ABC, abstractmethod
import hashlib
import base64

class BaseProductScraper(ABC):
    """
    产品信息提取基础类
    所有平台的scraper都需要继承这个类
    """
    def __init__(self, html_content=None, verbose=True):
        self.html_content = html_content
        self.soup = None
        self.verbose = verbose
        self.product_data = {
            "标题": "",
            "描述": "",
            "短描述": "",
            "价格": "",
            "产品图片": [],
            "产品分类": "",
            "产品变体": [],
            "SKU": "",
            "销量信息": "",
            "评分": "",
            "评论数": "",
            "视频链接": "",
            "品牌": "",
        }
        
        if self.verbose:
            print(f"初始化{self.get_platform_name()}爬虫，HTML内容长度: {len(html_content) if html_content else 0}")
    
    @abstractmethod
    def get_platform_name(self):
        """返回平台名称"""
        pass
    
    @abstractmethod
    def get_supported_domains(self):
        """返回支持的域名列表"""
        pass
    
    def load_html(self):
        """
        加载HTML内容
        """
        if self.verbose:
            print(f"加载HTML内容，长度: {len(self.html_content) if self.html_content else 0}")
        
        try:
            if not self.html_content:
                print("错误: HTML内容为空!")
                return False
                
            if self.verbose:
                print(f"开始解析HTML内容")
            
            self.soup = BeautifulSoup(self.html_content, 'html.parser')
            if self.verbose:
                print("成功创建BeautifulSoup解析树")
            return True
        except Exception as e:
            print(f"加载HTML内容错误: {e}")
            return False
    
    @abstractmethod
    def extract_product_info(self):
        """
        提取产品信息 - 每个平台需要实现自己的逻辑
        """
        pass
    
    def save_to_file(self, output_file="product_info.txt"):
        """
        将提取的信息保存到文本文件
        """
        try:
            if self.verbose:
                print(f"保存信息到文本文件: {output_file}")
                
            with open(output_file, 'w', encoding='utf-8') as file:
                file.write(f"{self.get_platform_name()}产品信息提取结果\n")
                file.write(f"=" * 50 + "\n\n")
                
                file.write(f"产品标题: {self.product_data['标题']}\n\n")
                file.write(f"产品描述: {self.product_data['描述']}\n\n")
                file.write(f"产品价格: {self.product_data['价格']}\n\n")
                file.write(f"产品SKU: {self.product_data['SKU']}\n\n")
                
                file.write(f"产品评分: {self.product_data['评分']}\n")
                file.write(f"评论数量: {self.product_data['评论数']}\n")
                file.write(f"销量信息: {self.product_data['销量信息']}\n\n")
                
                # 产品分类
                file.write(f"产品分类: {self.product_data['产品分类']}\n\n")
                
                # 产品图片链接
                if self.product_data['产品图片']:
                    images_str = "|||".join(self.product_data['产品图片'])
                    file.write(f"产品图片链接: {images_str}\n\n")
                else:
                    file.write(f"产品图片链接: \n\n")
                
                # 产品视频链接
                file.write(f"产品视频链接: {self.product_data['视频链接']}\n\n")
                
                # 产品变体信息
                self._write_variants_to_file(file)
                
                print(f"产品信息已保存到 {output_file}")
                return True
        except Exception as e:
            print(f"保存文件时出错: {e}")
            return False
    
    @abstractmethod
    def _write_variants_to_file(self, file):
        """
        将变体信息写入文件 - 每个平台可能有不同的格式
        """
        pass
    
    @abstractmethod
    def send_to_api(self, api_url, warehouse_id="3079", source_link=""):
        """
        发送数据到API - 每个平台可能有不同的数据格式
        """
        pass

class AmazonProductScraper(BaseProductScraper):
    """亚马逊平台产品信息提取器"""
    
    def __init__(self, html_content=None, verbose=True, session=None, headers=None, proxy=None):
        """
        初始化亚马逊产品信息提取器

        :param html_content: HTML内容
        :param verbose: 是否打印详细信息
        :param session: 请求会话（如果需要发送网络请求）
        :param headers: 请求头
        :param proxy: 代理
        """
        super().__init__(html_content, verbose)

        # 初始化网络相关参数
        self.session = session or requests.Session()
        self.headers = headers or get_headers_with_currency()
        self.proxy = proxy
        self.product_type = None
        self.product_group_id = None
        self.amazon_domain = None  # 动态检测的Amazon域名

        # 用于调试：存储批量价格接口返回的原始JSON文本
        self.debug_variant_price_json = []
    
    def get_platform_name(self):
        return "Amazon"
    
    def get_supported_domains(self):
        return ["amazon.com", "amazon.de", "amazon.co.uk", "amazon.fr", "amazon.it", "amazon.es"]

    def _detect_amazon_domain(self):
        """
        从HTML中检测Amazon域名
        :return: 检测到的Amazon域名，如 'amazon.de', 'amazon.com' 等
        """
        if not self.soup:
            return "amazon.de"  # 默认值

        try:
            # 方法1: 查找canonical URL
            canonical = self.soup.find('link', rel='canonical')
            if canonical and canonical.get('href'):
                url = canonical.get('href')
                domain_match = re.search(r'https?://(?:www\.)?(amazon\.[a-z.]+)', url)
                if domain_match:
                    domain = domain_match.group(1)
                    if domain in self.get_supported_domains():
                        if self.verbose:
                            print(f"从canonical URL检测到域名: {domain}")
                        return domain

            # 方法2: 查找Amazon链接
            amazon_links = self.soup.find_all('a', href=re.compile(r'https?://[^/]*amazon\.[^/]+'))
            if amazon_links:
                for link in amazon_links[:10]:  # 检查前10个链接
                    href = link.get('href', '')
                    domain_match = re.search(r'https?://(?:www\.)?(amazon\.[a-z.]+)', href)
                    if domain_match:
                        domain = domain_match.group(1)
                        if domain in self.get_supported_domains():
                            if self.verbose:
                                print(f"从页面链接检测到域名: {domain}")
                            return domain

            # 方法3: 从HTML内容中搜索Amazon URL
            html_content = str(self.soup)
            amazon_urls = re.findall(r'https?://(?:www\.)?(amazon\.[a-z.]+)', html_content)
            if amazon_urls:
                # 统计最常见的域名
                from collections import Counter
                domain_counts = Counter([domain for domain in amazon_urls if domain in self.get_supported_domains()])
                if domain_counts:
                    most_common_domain = domain_counts.most_common(1)[0][0]
                    if self.verbose:
                        print(f"从HTML内容统计检测到域名: {most_common_domain} (出现{domain_counts[most_common_domain]}次)")
                    return most_common_domain

            # 方法4: 从页面语言/地区信息推断
            lang_elem = self.soup.find('html')
            if lang_elem and lang_elem.get('lang'):
                lang = lang_elem.get('lang').lower()
                domain_mapping = {
                    'de': 'amazon.de',
                    'de-de': 'amazon.de',
                    'en-us': 'amazon.com',
                    'en-gb': 'amazon.co.uk',
                    'fr': 'amazon.fr',
                    'fr-fr': 'amazon.fr',
                    'it': 'amazon.it',
                    'it-it': 'amazon.it',
                    'es': 'amazon.es',
                    'es-es': 'amazon.es'
                }
                if lang in domain_mapping:
                    if self.verbose:
                        print(f"从页面语言推断域名: {domain_mapping[lang]} (lang={lang})")
                    return domain_mapping[lang]

        except Exception as e:
            if self.verbose:
                print(f"检测Amazon域名时出错: {e}")

        # 默认返回
        if self.verbose:
            print("使用默认域名: amazon.de")
        return "amazon.de"

    def set_amazon_domain(self, domain):
        """
        手动设置Amazon域名
        :param domain: Amazon域名，如 'amazon.de', 'amazon.com' 等
        """
        if domain in self.get_supported_domains():
            self.amazon_domain = domain
            if self.verbose:
                print(f"手动设置Amazon域名: {self.amazon_domain}")
        else:
            print(f"不支持的域名: {domain}，支持的域名: {self.get_supported_domains()}")
    
    def extract_product_info(self):
        """提取亚马逊产品信息"""
        if not self.load_html():
            print("请先加载HTML文件")
            return False

        # 检测Amazon域名
        self.amazon_domain = self._detect_amazon_domain()
        if self.verbose:
            print(f"检测到Amazon域名: {self.amazon_domain}")

        if self.verbose:
            print("开始提取亚马逊产品信息...")

        # 初始化产品数据结构
        self.product_data = {
            "标题": "",
            "价格": "",
            "描述": "",
            "短描述": "",  # 明确添加短描述字段
            "SKU": "",
            "产品图片": [],
            "产品分类": "",
            "产品变体": [],
            "评分": "",
            "评论数": "",
            "销量信息": "",
            "视频链接": "",
        }

        self._extract_title()
        
        # 检查标题是否存在
        if not self.product_data["标题"]:
            if self.verbose:
                print("⚠️ 未能获取产品标题，页面可能受限")
            return False
        
        # 继续提取其他信息
        self._extract_description()  # 包含尺码表、包装尺寸和高级表格
        self._extract_images()
        self._extract_categories()
        self._extract_variants()  # 先提取变体
        self._extract_price()     # 再提取价格，这样可以使用变体价格作为备选
        self._extract_sku()
        self._extract_ratings()
        self._extract_video_url()
        self._extract_short_description()  # 确保调用短描述提取方法
        
        if self.verbose:
            print("亚马逊产品信息提取完成")
            print("-" * 30)
            print(f"标题: {self.product_data['标题']}")
            print(f"描述长度: {len(self.product_data['描述'])}")
            print(f"价格: {self.product_data['价格']}")
            print(f"SKU: {self.product_data['SKU']}")
            print(f"找到 {len(self.product_data['产品图片'])} 张产品图片")
            print(f"视频链接: {self.product_data['视频链接'] if self.product_data['视频链接'] else '无'}")
            print(f"产品分类: {self.product_data['产品分类']}")
            # 确保产品变体存在且不为None
            variants_count = len(self.product_data['产品变体']) if self.product_data.get('产品变体') is not None else 0
            print(f"找到 {variants_count} 种产品变体")
            print(f"评分: {self.product_data['评分']}")
            print(f"评论数: {self.product_data['评论数']}")
            print("-" * 30)
        
        return True
    
    def _extract_title(self):
        """提取产品标题"""
        try:
            if self.verbose:
                print("提取亚马逊产品标题...")
            
            # 亚马逊标题提取选择器
            title_elem = self.soup.select_one("#productTitle") or \
                        self.soup.select_one("h1.a-size-large")
            
            if title_elem:
                full_title = title_elem.get_text().strip()
                if self.verbose:
                    print(f"找到原始标题: {full_title[:50]}...")
                
                # 提取供应商/店铺名称
                brand_name = ""
                byline_elem = self.soup.select_one("#bylineInfo")
                if byline_elem:
                    # 提取显示文本
                    brand_name = byline_elem.get_text().strip()
                    
                    # 使用模式：开始<a id="bylineInfo" class="a-link-normal" href="(*)/stores/ 结束 /page/
                    if 'href' in byline_elem.attrs:
                        href = byline_elem['href']
                        store_match = re.search(r'/stores/([^/]+)/page/', href)
                        if store_match:
                            store_id = store_match.group(1)
                            if self.verbose:
                                print(f"找到店铺ID: {store_id}")
                    
                    if self.verbose:
                        print(f"找到供应商/品牌: {brand_name}")
                
                # 从标题中移除品牌名称（如果存在）
                if brand_name and brand_name in full_title:
                    # 移除品牌名，可能包含 "Visit the X Store" 或 "Brand: X" 格式
                    clean_title = full_title
                    
                    # 尝试多种常见格式
                    brand_patterns = [
                        # 提取实际品牌名（去除"Visit the"或"Brand:"等前缀）
                        re.search(r'Visit the (.*?) Store', brand_name),
                        re.search(r'Marke: (.*?)$', brand_name),  # 德语
                        re.search(r'Brand: (.*?)$', brand_name),  # 英语
                        re.search(r'Besuchen Sie den (.*?)-Store', brand_name)  # 德语
                    ]
                    
                    # 使用找到的第一个有效匹配
                    actual_brand = None
                    for pattern_match in brand_patterns:
                        if pattern_match:
                            actual_brand = pattern_match.group(1).strip()
                            break
                    
                    # 如果未能提取出品牌，使用整个bylineInfo文本
                    if not actual_brand:
                        actual_brand = brand_name
                    
                    # 从标题中移除品牌名称
                    if actual_brand and actual_brand in clean_title:
                        clean_title = clean_title.replace(actual_brand, "").strip()
                        # 清理多余的标点符号
                        clean_title = re.sub(r'\s+[-,]\s+', ' ', clean_title)
                        clean_title = clean_title.strip()
                        
                        if self.verbose:
                            print(f"已从标题中移除品牌 '{actual_brand}'")
                            print(f"清理后的标题: {clean_title[:50]}...")
                        
                        self.product_data["标题"] = clean_title
                    else:
                        self.product_data["标题"] = full_title
                else:
                    self.product_data["标题"] = full_title
                
                if self.verbose:
                    print(f"最终标题: {self.product_data['标题'][:50]}...")
        except Exception as e:
            print(f"提取亚马逊标题时出错: {e}")
    
    def _clean_html_content(self, html_content):
        """
        清理HTML内容，去除JS、CSS和其他无用内容，专注保留尺码表和包装尺寸信息
        """
        if not html_content:
            return ""
            
        try:
            # 预处理：使用更强大的正则表达式移除脚本和样式
            html_content = re.sub(r'<script[^>]*>[\s\S]*?</script>', '', html_content, flags=re.DOTALL)
            html_content = re.sub(r'<style[^>]*>[\s\S]*?</style>', '', html_content, flags=re.DOTALL)
            html_content = re.sub(r'<!--[\s\S]*?-->', '', html_content, flags=re.DOTALL)
            # 移除noscript标签及其内容
            html_content = re.sub(r'<noscript[^>]*>[\s\S]*?</noscript>', '', html_content, flags=re.DOTALL)
            
            # 移除所有JavaScript事件属性
            js_event_pattern = r' on\w+="[^"]*"'
            html_content = re.sub(js_event_pattern, '', html_content)
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除所有script标签及其内容
            for script in soup.find_all("script"):
                script.decompose()
                
            # 移除所有style标签及其内容
            for style in soup.find_all("style"):
                style.decompose()
            
            # 移除所有noscript标签及其内容
            for noscript in soup.find_all("noscript"):
                noscript.decompose()
                
            # 移除所有iframe标签
            for iframe in soup.find_all("iframe"):
                iframe.decompose()
                
            # 移除所有form标签及其内容
            for form in soup.find_all("form"):
                form.decompose()
                
            # 移除所有button标签及其内容
            for button in soup.find_all("button"):
                button.decompose()
                
            # 移除所有input标签
            for input_tag in soup.find_all("input"):
                input_tag.decompose()
                
            # 移除所有a标签但保留其内容
            for a_tag in soup.find_all("a"):
                a_tag.replace_with(a_tag.get_text())
                
            # 移除所有注释
            for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
                comment.extract()
                
            # 移除所有空div和span
            for tag in soup.find_all(['div', 'span']):
                if not tag.get_text().strip():
                    tag.decompose()
            
            # 移除危险的JavaScript属性
            js_attrs = ['onclick', 'onmouseover', 'onmouseout', 'onload', 'onerror', 
                       'onchange', 'onfocus', 'onblur', 'onsubmit', 'onreset']
            
            for attr in js_attrs:
                for tag in soup.find_all(attrs={attr: True}):
                    del tag[attr]
            
            # 保留尺码表和包装尺寸相关的标签
            relevant_tags = []
            
            # 检查所有表格是否为尺码表
            for table in soup.find_all('table'):
                table_text = table.get_text().lower()
                
                # 尺码表相关关键词
                size_keywords = ['size', 'chart', 'größe', 'measurement', 'dimensions']
                # 如果表格包含尺码关键词或尺寸值
                if any(keyword in table_text for keyword in size_keywords) or re.search(r'[sml]|xs|xl|xxl|\d+\s*cm|\d+\s*inch', table_text):
                    # 确保不是产品对比表格
                    if not re.search(r'(price|preis).*?(price|preis)', table_text):
                        relevant_tags.append(table)
            
            # 检查包含包装尺寸信息的元素
            packaging_keywords = ['package', 'dimensions', 'weight', 'abmessungen', 'gewicht', 'shipping']
            for tag in soup.find_all(['div', 'span', 'li', 'p', 'tr']):
                tag_text = tag.get_text().lower()
                
                # 如果包含包装关键词和尺寸数据
                if any(keyword in tag_text for keyword in packaging_keywords) and re.search(r'\d+[.,]?\d*\s*(cm|mm|inch|g|kg)', tag_text):
                    relevant_tags.append(tag)
            
            # 如果找到了相关标签，只保留这些标签
            if relevant_tags:
                # 创建新的soup对象
                new_soup = BeautifulSoup('', 'html.parser')
                container = new_soup.div
                
                # 添加所有相关标签
                for tag in relevant_tags:
                    container.append(tag)
                
                return str(new_soup)
            else:
                # 如果没有找到明确的尺码表或包装信息，返回清理过的原始HTML
                return str(soup)
            
        except Exception as e:
            if self.verbose:
                print(f"清理HTML内容时出错: {e}")
                traceback.print_exc()
            return html_content
    
    def save_to_file(self, output_file="amazon_product.txt"):
        """
        将提取的信息保存到文本文件
        :param output_file: 输出文件路径
        """
        try:
            if self.verbose:
                print(f"保存信息到文本文件: {output_file}")
                
            with open(output_file, 'w', encoding='utf-8') as file:
                file.write(f"{self.get_platform_name()}产品信息提取结果\n")
                file.write(f"=" * 50 + "\n\n")
                
                file.write(f"产品标题: {self.product_data['标题']}\n\n")
                
                # 直接使用描述，不再使用短描述作为替代
                file.write(f"产品描述: {self.product_data['描述']}\n\n")
                
                # 添加短描述
                file.write(f"短描述: {self.product_data.get('短描述', '')}\n\n")
                
                file.write(f"产品价格: {self.product_data['价格']}\n\n")
                file.write(f"产品SKU: {self.product_data['SKU']}\n\n")
                
                file.write(f"产品评分: {self.product_data['评分']}\n")
                file.write(f"评论数量: {self.product_data['评论数']}\n")
                file.write(f"销量信息: {self.product_data['销量信息']}\n\n")
                
                # 产品分类 - 使用|||分割符
                file.write(f"产品分类: {self.product_data['产品分类']}\n\n")
                
                # 产品图片链接 - 使用单行|||分隔符
                if self.product_data['产品图片']:
                    images_str = "|||".join(self.product_data['产品图片'])
                    file.write(f"产品图片链接: {images_str}\n\n")
                else:
                    file.write(f"产品图片链接: \n\n")
                
                # 产品视频链接
                file.write(f"产品视频链接: {self.product_data['视频链接']}\n\n")
                
                # 产品变体信息
                self._write_variants_to_file(file)
                
                print(f"{self.get_platform_name()}产品信息已保存到 {output_file}")
                return True
        except Exception as e:
            print(f"保存文件时出错: {e}")
            return False
    
    @abstractmethod
    def _write_variants_to_file(self, file):
        """
        将变体信息写入文件 - 每个平台可能有不同的格式
        """
        pass
    
    @abstractmethod
    def send_to_api(self, api_url, warehouse_id="3079", source_link=""):
        """
        发送数据到API - 每个平台可能有不同的数据格式
        """
        pass

    def _extract_description(self):
        """提取产品描述 - 只包含尺码表(HTML)和包装尺寸信息(纯文本)"""
        try:
            if self.verbose:
                print("提取亚马逊产品描述(尺码表 + 包装尺寸)...")
                
            # 1. 提取尺码表（只查找真正的尺码表）- 保留HTML格式
            size_chart_html = self._extract_size_chart()
            
            # 2. 提取包装尺寸信息 - 纯文本格式
            packaging_text = self._extract_packaging_dimensions()
            
            # 3. 组合结果
            if size_chart_html or packaging_text:
                # 创建最终描述内容
                final_content = ""
                
                # 处理尺码表 (HTML格式)
                if size_chart_html:
                    # 清理尺码表HTML内容
                    clean_size_chart = self._clean_html_content(size_chart_html)
                    
                    # 验证尺码表HTML
                    soup = BeautifulSoup(clean_size_chart, 'html.parser')
                    
                    # 只保留表格元素
                    tables = soup.find_all('table')
                    if tables:
                        size_chart_content = '<div class="size-chart-section">'
                        for table in tables:
                            size_chart_content += str(table)
                        size_chart_content += '</div>'
                        final_content += size_chart_content
                
                # 处理包装尺寸信息 (纯文本格式)
                if packaging_text:
                    # 如果已经有尺码表，添加分隔符
                    if final_content:
                        final_content += '<br/><br/>'
                    
                    # 格式化包装尺寸文本
                    packaging_lines = packaging_text.split('\n')
                    packaging_content = '<p>'
                    
                    for line in packaging_lines:
                        if line.strip():
                            packaging_content += f'{line}<br/>'
                    
                    packaging_content += '</p>'
                    final_content += packaging_content
                
                # 最终的HTML格式调整
                final_content = f'{final_content}'
                
                self.product_data["描述"] = final_content
                
                if self.verbose:
                    print(f"成功提取描述内容，长度: {len(final_content)} 字符")
                return True
            else:
                self.product_data["描述"] = ""
                if self.verbose:
                    print("未找到尺码表或包装尺寸信息")
                return False
                
        except Exception as e:
            print(f"提取亚马逊产品描述时出错: {e}")
            self.product_data["描述"] = ""
            return False
    
    def _extract_size_chart(self):
        """专门提取尺码表"""
        try:
            size_chart_html = ""
            
            # 尺码表相关的关键词
            size_keywords = [
                'size chart', 'größentabelle', 'size guide', 'sizing chart',
                'measurement', 'measurements', 'maße', 'größe', '尺码表'
            ]
            
            # 查找可能包含尺码表的容器
            containers = [
                self.soup.select_one("#aplus_feature_div"),
                self.soup.select_one("#productDescription_feature_div"),
                self.soup.select_one("#feature-bullets"),
                # 添加对aplus-premium模块的支持
                self.soup.select_one("div.celwidget.aplus-module.premium-module-5-comparison-table-scroller.aplus-premium"),
                # 更广泛地选择所有aplus-module元素
                self.soup.select_one("div.aplus-module")
            ]
            
            containers = [c for c in containers if c]
            
            for container in containers:
                # 查找表格
                tables = container.find_all('table')
                
                for table in tables:
                    table_text = table.get_text().lower().strip()
                    
                    # 检查是否包含尺码相关关键词
                    contains_size_keywords = any(keyword in table_text for keyword in size_keywords)
                    
                    # 检查表格结构（尺码表通常有多行多列）
                    rows = table.find_all('tr')
                    has_good_structure = len(rows) >= 2
                    
                    if has_good_structure:
                        # 检查第一行是否有多列
                        first_row_cells = rows[0].find_all(['th', 'td'])
                        has_multiple_columns = len(first_row_cells) >= 2
                        
                        # 检查是否包含尺寸相关的内容
                        contains_size_values = any(
                            re.search(r'[sml]|xs|xl|xxl|\d+["\']|\d+\s*cm|\d+\s*inch', cell.get_text().lower()) 
                            for cell in first_row_cells
                        )
                        
                        # 额外检查：确认这不是产品对比表格
                        is_product_comparison = False
                        # 检查表格是否包含产品名称、价格等特征
                        if re.search(r'(price|preis|kaufen|buy|product|produkt|asin|model)', table_text):
                            # 检查是否包含多个产品的对比
                            products_count = len(re.findall(r'(€|EUR|USD|\$)\s*\d+', table_text))
                            if products_count > 1:
                                is_product_comparison = True
                                if self.verbose:
                                    print(f"跳过产品对比表格 (包含{products_count}个价格)")
                        
                        # 最终判断：确定这是一个真正的尺码表
                        if (contains_size_keywords or contains_size_values) and has_multiple_columns and not is_product_comparison:
                            # 仅提取表格本身，确保不包含周围的内容
                            size_chart_html += f'<div class="size-chart-table">{str(table)}</div>'
                            if self.verbose:
                                print(f"找到尺码表: {len(rows)}行 x {len(first_row_cells)}列")
                            break
                
                if size_chart_html:
                    break
            
            return size_chart_html
            
        except Exception as e:
            if self.verbose:
                print(f"提取尺码表时出错: {e}")
            return ""
    
    def _extract_packaging_dimensions(self):
        """提取包装尺寸信息 - 纯文本版本"""
        try:
            packaging_text = ""
            
            # 包装尺寸关键词（多语言）
            packaging_keywords = [
                'verpackungsabmessungen', 'paketabmessungen', 'produktabmessungen',
                'package dimensions', 'product dimensions', 'item dimensions',
                'shipping weight', 'versandgewicht', 'abmessungen', 'gewicht',
                'weight', 'dimensions', 'height', 'width', 'depth', 'höhe', 'breite', 'tiefe'
            ]
            
            # 准确的包装尺寸正则表达式模式
            dimension_patterns = [
                r'\d+[.,]?\d*\s*x\s*\d+[.,]?\d*\s*x\s*\d+[.,]?\d*\s*(cm|mm|inch|inches)',  # 10 x 5 x 2 cm
                r'\d+[.,]?\d*\s*(cm|mm|inch|inches)\s*x\s*\d+[.,]?\d*\s*(cm|mm|inch|inches)',  # 10cm x 5cm
                r'\d+[.,]?\d*\s*(kg|g|gramm|pounds|lbs)',  # 重量模式
                r'(length|width|height|länge|breite|höhe).*?\d+[.,]?\d*\s*(cm|mm|inch|inches)'  # 长宽高带数值
            ]
            
            # 查找包装信息的容器
            containers = [
                self.soup.select_one("#productDetails_detailBullets_sections1"),
                self.soup.select_one("#productDetails_db_sections"),
                self.soup.select_one("#detailBullets_feature_div"),
                self.soup.select_one("#prodDetails")
            ]
            
            containers = [c for c in containers if c]
            packaging_info_list = []
            
            for container in containers:
                # 方法1: 查找列表项
                list_items = container.select('span.a-list-item, li')
                
                for item in list_items:
                    item_text = item.get_text().strip()
                    item_text_lower = item_text.lower()
                    
                    # 检查是否包含包装尺寸关键词
                    contains_keyword = any(keyword in item_text_lower for keyword in packaging_keywords)
                    
                    # 检查是否包含尺寸数据
                    contains_dimension = any(re.search(pattern, item_text_lower) for pattern in dimension_patterns)
                    
                    if contains_keyword and contains_dimension:
                        # 确保这是包装信息而不是其他内容
                        if not re.search(r'(buy|purchase|kaufen|add to cart|in den einkaufswagen)', item_text_lower):
                            # 清理文本，去除多余空格
                            clean_text = re.sub(r'\s+', ' ', item_text).strip()
                            packaging_info_list.append(clean_text)
                            if self.verbose:
                                print(f"找到包装信息: {clean_text[:80]}...")
                            break
                
                # 方法2: 查找表格行
                if not packaging_info_list:
                    table_rows = container.select('tr')
                    
                    for row in table_rows:
                        row_text = row.get_text().strip()
                        row_text_lower = row_text.lower()
                        
                        # 检查是否包含包装尺寸关键词
                        contains_keyword = any(keyword in row_text_lower for keyword in packaging_keywords)
                        
                        # 检查是否包含尺寸数据
                        contains_dimension = any(re.search(pattern, row_text_lower) for pattern in dimension_patterns)
                        
                        if contains_keyword and contains_dimension:
                            # 确保这是包装信息而不是其他内容
                            if not re.search(r'(buy|purchase|kaufen|add to cart|in den einkaufswagen)', row_text_lower):
                                # 清理文本，去除多余空格
                                clean_text = re.sub(r'\s+', ' ', row_text).strip()
                                packaging_info_list.append(clean_text)
                                if self.verbose:
                                    print(f"找到包装信息(表格): {clean_text[:80]}...")
                                break
                
                if packaging_info_list:
                    break
            
            # 组合所有找到的包装信息
            if packaging_info_list:
                packaging_text = "\n".join(packaging_info_list)
            
            return packaging_text
            
        except Exception as e:
            if self.verbose:
                print(f"提取包装尺寸信息时出错: {e}")
            return ""
    
    def _extract_aplus_tables(self):
        """提取高级模块表格(aplus-premium) - 已禁用"""
        # 此方法已被禁用，不再提取高级对比表格
        if self.verbose:
            print("高级对比表格提取已禁用")
        return ""
    
    def _extract_images(self):
        """提取产品图片 - 改进版：始终全局扫描 hiRes URL"""
        try:
            if self.verbose:
                print("提取亚马逊产品图片...")
            
            html_content = str(self.soup)
            
            # 1. 使用_extract_color_images_mapping 方法（保持用于后续变体映射）
            color_images = self._extract_color_images_mapping()
            if color_images and self.verbose:
                print(f"从colorImages中提取到 {len(color_images)} 张图片 (用于颜色映射)")
            
            images = list(color_images.values())
            
            # 2. 无论已有多少图片，都全局搜索 "hiRes":"URL" / 'hiRes':'URL'
            hi_res_patterns = [
                r'"hiRes"\s*:\s*"(https://[^"\\]+?)"',  # 双引号
                r"'hiRes'\s*:\s*'(https://[^'\\]+?)'"        # 单引号
            ]
            for pattern in hi_res_patterns:
                matches = re.findall(pattern, html_content)
                for url in matches:
                    if url and url.startswith('https://') and url not in images:
                        images.append(url)
                        if self.verbose:
                            print(f"全局hiRes匹配到图片: {url.split('/')[-1]}")
            
            # 3. 去重并限制数量
            unique_images = []
            for img in images:
                if img not in unique_images and "sprite" not in img:
                    unique_images.append(img)
            
            self.product_data["产品图片"] = unique_images[:15]  # 最多 15 张
            
            if self.verbose:
                print(f"最终提取到 {len(self.product_data['产品图片'])} 张产品图片")
            
        except Exception as e:
            print(f"提取亚马逊图片时出错: {e}")
            traceback.print_exc()
    
    def _extract_categories(self):
        """提取产品分类"""
        try:
            if self.verbose:
                print("提取亚马逊产品分类...")
            
            categories = []
            
            # 方法1: 从导航栏提取
            breadcrumbs = self.soup.select("#wayfinding-breadcrumbs_feature_div li")
            if breadcrumbs:
                for crumb in breadcrumbs:
                    category_text = crumb.get_text().strip()
                    # 去除导航符号 › 和清理空白
                    category_text = category_text.replace('›', '').strip()
                    if category_text:
                        categories.append(category_text)
                
                if self.verbose:
                    print(f"从导航栏提取到 {len(categories)} 个分类")
            
            # 方法2: 从产品信息中提取
            if not categories:
                category_row = self.soup.select_one("tr:-soup-contains('Best Sellers Rank')")
                if category_row:
                    category_links = category_row.select("a")
                    for link in category_links:
                        category_text = link.get_text().strip()
                        # 确保去除任何导航符号和清理空白
                        category_text = category_text.replace('›', '').strip()
                        if category_text:
                            categories.append(category_text)
                    
                    if self.verbose:
                        print(f"从Best Sellers Rank提取到 {len(categories)} 个分类")
            
            # 使用|||连接分类
            if categories:
                self.product_data["产品分类"] = "|||".join(categories)
                if self.verbose:
                    print(f"最终分类: {self.product_data['产品分类']}")
                
        except Exception as e:
            print(f"提取亚马逊分类时出错: {e}")
    
    def _extract_variants(self):
        """提取产品变体"""
        try:
            if self.verbose:
                print("提取亚马逊产品变体...")
            
            # 首先尝试从inlineTwisterData中提取变体
            variants = self._extract_variants_from_twister_data()
            
            # 如果没有找到变体，则尝试其他方法
            if not variants:
                if self.verbose:
                    print("从inlineTwisterData未找到变体，尝试其他方法...")
                
                # 方法2: 从选择区域提取
                variant_elems = self.soup.select("#variation_color_name li, #variation_size_name li")
                
                for elem in variant_elems:
                    try:
                        if elem.has_attr("data-defaultasin"):
                            variant_info = {
                                'asin': elem.get("data-defaultasin", ""),
                                'specs': {}
                            }
                            
                            # 提取规格类型和值
                            if "variation_color_name" in elem.parent.get("id", ""):
                                variant_info['specs']["颜色"] = elem.get_text().strip()
                            elif "variation_size_name" in elem.parent.get("id", ""):
                                variant_info['specs']["尺寸"] = elem.get_text().strip()
                            
                            if variant_info['specs']:
                                variants.append(variant_info)
                                if self.verbose:
                                    specs_str = ", ".join(f"{k}: {v}" for k, v in variant_info['specs'].items())
                                    print(f"从选择区域找到变体: {specs_str}")
                    except Exception as e:
                        if self.verbose:
                            print(f"提取单个变体时出错: {e}")
            
            # 使用TwisterData为变体补充价格和库存、图片信息
            try:
                twister_data_for_enrich = self._load_twister_data(str(self.soup))
                if twister_data_for_enrich:
                    if self.verbose:
                        print("检测到TwisterData，开始丰富变体价格和图片信息...")
                        
                    # 提取产品类型和组ID (需要传递给AJAX请求)
                    if "productGroupId" in twister_data_for_enrich:
                        self.product_group_id = twister_data_for_enrich["productGroupId"]
                    if "productTypeDefinition" in twister_data_for_enrich:
                        self.product_type = twister_data_for_enrich["productTypeDefinition"]
                    
                    # 优先使用批量API获取变体价格
                    enriched_variants = self._enrich_variants_with_price_stock(variants, twister_data_for_enrich)
                    # 确保不会因为方法返回None而丢失原始变体数据
                    if enriched_variants is not None:
                        variants = enriched_variants
                    
                    # 继续丰富图片信息
                    enriched_variants = self._enrich_variants_with_images(variants, twister_data_for_enrich)
                    # 同样确保不会丢失数据
                    if enriched_variants is not None:
                        variants = enriched_variants
                    
                    if self.verbose:
                        # 确保variants不为None再进行迭代
                        if variants is not None:
                            prices = [v.get('sale_price') for v in variants if v is not None and 'sale_price' in v]
                            if prices:
                                print(f"变体价格范围: {min(prices)} - {max(prices)}")
                            else:
                                print("没有成功获取到变体价格")
                        else:
                            print("变体列表为None，无法获取价格信息")
            except Exception as e:
                if self.verbose:
                    print(f"丰富变体价格/图片信息时出错: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 确保variants不为None
            if variants is None:
                variants = []
                
            self.product_data["产品变体"] = variants
            
            if self.verbose:
                print(f"共找到 {len(variants)} 种产品变体")
            
        except Exception as e:
            print(f"提取亚马逊变体时出错: {e}")
            # 确保异常情况下也设置一个空列表
            self.product_data["产品变体"] = []
    
    def _load_twister_data(self, html_content):
        """
        从HTML中提取twisterData对象
        尝试模拟JavaScript执行加载twisterData
        """
        try:
            # 尝试找到包含变体数据的脚本部分
            twister_script_pattern = r'<script[^>]*>.*?"inlineTwisterData"\s*:.*?</script>'
            script_match = re.search(twister_script_pattern, html_content, re.DOTALL)
            
            if script_match:
                script_content = script_match.group(0)
                
                # 直接提取JSON对象
                data_pattern = r'"inlineTwisterData"\s*:\s*(\{.*?),"parentAsin"'
                data_match = re.search(data_pattern, script_content, re.DOTALL)
                
                if data_match:
                    twister_json = data_match.group(1) + "}"
                    # 提取其他关键属性
                    parent_asin = re.search(r'"parentAsin"\s*:\s*"([^"]+)"', script_content)
                    current_asin = re.search(r'"currentAsin"\s*:\s*"([^"]+)"', script_content)
                    dimension_to_asin_map = re.search(r'"dimensionToAsinMap"\s*:\s*(\{.*?\})', script_content, re.DOTALL)
                    variation_values = re.search(r'"variationValues"\s*:\s*(\{.*?\})', script_content, re.DOTALL)
                    dimensions = re.search(r'"dimensions"\s*:\s*(\[.*?\])', script_content, re.DOTALL)
                    image_hash_map = re.search(r'"imageHashMap"\s*:\s*(\{.*?\})', script_content, re.DOTALL)
                    dimension_values_display_data = re.search(r'"dimensionValuesDisplayData"\s*:\s*(\{.*?\})', script_content, re.DOTALL)
                    # 提取维度价格库存数据 dimensionValuesData
                    dimension_values_data_match = re.search(r'"dimensionValuesData"\s*:\s*(\{.*?\})', script_content, re.DOTALL)
                    
                    # 构建完整的变体数据对象
                    twister_data = {
                        "inlineTwisterData": json.loads(self._fix_json_string(twister_json)) if self._is_valid_json(self._fix_json_string(twister_json)) else {},
                        "parentAsin": parent_asin.group(1) if parent_asin else "",
                        "currentAsin": current_asin.group(1) if current_asin else "",
                        "dimensionToAsinMap": json.loads(self._fix_json_string(dimension_to_asin_map.group(1))) if dimension_to_asin_map and self._is_valid_json(self._fix_json_string(dimension_to_asin_map.group(1))) else {},
                        "variationValues": json.loads(self._fix_json_string(variation_values.group(1))) if variation_values and self._is_valid_json(self._fix_json_string(variation_values.group(1))) else {},
                        "dimensions": json.loads(self._fix_json_string(dimensions.group(1))) if dimensions and self._is_valid_json(self._fix_json_string(dimensions.group(1))) else [],
                        "imageHashMap": json.loads(self._fix_json_string(image_hash_map.group(1))) if image_hash_map and self._is_valid_json(self._fix_json_string(image_hash_map.group(1))) else {},
                        "dimensionValuesDisplayData": json.loads(self._fix_json_string(dimension_values_display_data.group(1))) if dimension_values_display_data and self._is_valid_json(self._fix_json_string(dimension_values_display_data.group(1))) else {},
                        "dimensionValuesData": json.loads(self._fix_json_string(dimension_values_data_match.group(1))) if dimension_values_data_match and self._is_valid_json(self._fix_json_string(dimension_values_data_match.group(1))) else {}
                    }
                    
                    if self.verbose:
                        print("成功加载TwisterData对象")
                        for key, value in twister_data.items():
                            if key == "inlineTwisterData":
                                print(f"  {key}: {...}")
                            elif isinstance(value, dict):
                                print(f"  {key}: {{{len(value)} items}}")
                            elif isinstance(value, list):
                                print(f"  {key}: [{len(value)} items]")
                            else:
                                print(f"  {key}: {value}")
                    
                    return twister_data
            
            # 如果无法通过常规方式提取，尝试提取原始脚本部分
            raw_script = re.search(r'<script[^>]*>(.*?inlineTwisterData.*?)</script>', html_content, re.DOTALL)
            if raw_script:
                # 简单提取JSON部分
                json_parts = {}
                
                # 提取维度列表
                dimensions_match = re.search(r'"dimensions"\s*:\s*(\[.*?\])', raw_script.group(1), re.DOTALL)
                if dimensions_match:
                    try:
                        dimensions_json = self._fix_json_string(dimensions_match.group(1))
                        json_parts["dimensions"] = json.loads(dimensions_json)
                    except:
                        pass
                
                # 提取ASIN映射
                asin_map_match = re.search(r'"dimensionToAsinMap"\s*:\s*(\{.*?\})', raw_script.group(1), re.DOTALL)
                if asin_map_match:
                    try:
                        asin_map_json = self._fix_json_string(asin_map_match.group(1))
                        json_parts["dimensionToAsinMap"] = json.loads(asin_map_json)
                    except:
                        pass
                
                # 提取变体值
                var_values_match = re.search(r'"variationValues"\s*:\s*(\{.*?\})', raw_script.group(1), re.DOTALL)
                if var_values_match:
                    try:
                        var_values_json = self._fix_json_string(var_values_match.group(1))
                        json_parts["variationValues"] = json.loads(var_values_json)
                    except:
                        pass
                
                # 提取图片映射
                image_map_match = re.search(r'"imageHashMap"\s*:\s*(\{.*?\})', raw_script.group(1), re.DOTALL)
                if image_map_match:
                    try:
                        image_map_json = self._fix_json_string(image_map_match.group(1))
                        json_parts["imageHashMap"] = json.loads(image_map_json)
                    except:
                        pass
                
                if json_parts:
                    if self.verbose:
                        print("成功从脚本中提取部分变体数据")
                        for key, value in json_parts.items():
                            if isinstance(value, dict):
                                print(f"  {key}: {{{len(value)} items}}")
                            elif isinstance(value, list):
                                print(f"  {key}: [{len(value)} items]")
                            else:
                                print(f"  {key}: {value}")
                    
                    return json_parts
        
        except Exception as e:
            if self.verbose:
                print(f"加载TwisterData对象时出错: {e}")
                traceback.print_exc()
        
        return {}
    
    def _is_valid_json(self, json_str):
        """检查JSON字符串是否有效"""
        try:
            json.loads(json_str)
            return True
        except:
            return False
    
    def _extract_variants_from_twister_data(self):
        """从Twister数据中提取变体信息"""
        try:
            if self.verbose:
                print("从Twister数据中提取变体信息...")
            
            variants = []
            html_content = str(self.soup)
            
            # 提取维度显示名称 (多语言支持)
            dimension_display_names = self._get_dimension_display_names(html_content)
            if dimension_display_names and self.verbose:
                print(f"维度显示名称: {dimension_display_names}")
            
            # 首先尝试直接从测试数据2.html格式提取（专门针对测试数据优化）
            try:
                # 查找测试数据2.html格式的变体数据
                test_data_pattern = r'"inlineTwisterData"\s*:.*?"parentAsin"\s*:\s*"([^"]+)"'
                test_data_match = re.search(test_data_pattern, html_content, re.DOTALL)
                
                if test_data_match:
                    if self.verbose:
                        print("检测到测试数据2.html格式的变体数据")
                    
                    # 提取dimensions数组
                    dimensions = []
                    dim_match = re.search(r'"dimensions"\s*:\s*\[(.*?)\]', html_content, re.DOTALL)
                    if dim_match:
                        # 提取引号中的字符串
                        dims = re.findall(r'"([^"]+)"', dim_match.group(1))
                        dimensions = dims
                        if self.verbose:
                            print(f"提取到维度列表: {dimensions}")
                            
                            # 输出维度的显示名称
                            for dim in dimensions:
                                display_name = dimension_display_names.get(dim, dim)
                                print(f"  {dim} 显示名称: {display_name}")
                    
                    # 提取variationValues对象
                    var_values = {}
                    var_match = re.search(r'"variationValues"\s*:\s*(\{.*?\})(?:,|\s*")', html_content, re.DOTALL)
                    if var_match:
                        var_json = var_match.group(1)
                        # 手动解析变体值
                        try:
                            # 修复JSON字符串
                            fixed_json = self._fix_json_string(var_json)
                            var_values = json.loads(fixed_json)
                            
                            # 如果加载成功，检查格式
                            for key, value in var_values.items():
                                if not isinstance(value, list):
                                    # 尝试转换为列表
                                    var_values[key] = list(value.values()) if isinstance(value, dict) else [value]
                            
                            if self.verbose:
                                print(f"提取到变体值: {list(var_values.keys())}")
                                for key, value in var_values.items():
                                    print(f"  {key}: {value}")
                        except Exception as e:
                            if self.verbose:
                                print(f"解析variationValues时出错: {e}")
                                print(f"尝试手动解析")
                            
                            # 手动提取键值对
                            key_vals = re.findall(r'"([^"]+)"\s*:\s*\[(.*?)\]', var_json)
                            for key, vals in key_vals:
                                val_list = []
                                val_items = re.findall(r'"([^"]+)"', vals)
                                var_values[key] = val_items
                                if self.verbose:
                                    print(f"  手动解析 {key}: {val_items}")
                    
                    # 提取dimensionToAsinMap对象
                    asin_map = {}
                    asin_match = re.search(r'"dimensionToAsinMap"\s*:\s*(\{.*?\})(?:,|\s*")', html_content, re.DOTALL)
                    if asin_match:
                        asin_json = asin_match.group(1)
                        try:
                            fixed_json = self._fix_json_string(asin_json)
                            asin_map = json.loads(fixed_json)
                            
                            if self.verbose:
                                print(f"提取到ASIN映射，共 {len(asin_map)} 个组合")
                        except Exception as e:
                            if self.verbose:
                                print(f"解析dimensionToAsinMap时出错: {e}")
                                print(f"尝试手动解析")
                            
                            # 手动提取键值对
                            asin_pairs = re.findall(r'"([^"]+)"\s*:\s*"([^"]+)"', asin_json)
                            for dim_key, asin in asin_pairs:
                                asin_map[dim_key] = asin
                                if self.verbose and len(asin_map) <= 5:  # 只显示前5个
                                    print(f"  手动解析 {dim_key}: {asin}")
                    
                    # 提取imageHashMap对象
                    image_map = {}
                    img_match = re.search(r'[\'\"]imageHashMap[\'\"]\s*:\s*[\'\"]({.*?)[\'\"]', html_content, re.DOTALL)
                    if img_match:
                        img_json = img_match.group(1)
                        try:
                            fixed_json = self._fix_json_string(img_json)
                            image_map = json.loads(fixed_json)
                            
                            if self.verbose:
                                print(f"提取到图片映射，共 {len(image_map)} 个图片")
                                
                                # 解析各个颜色的图片映射关系
                                color_dim = next((dim for dim in dimensions if 'color' in dim.lower()), None)
                                if color_dim and color_dim in var_values:
                                    print("颜色-图片对应关系:")
                                    for i, color in enumerate(var_values[color_dim]):
                                        # 尝试多种键格式
                                        for key_format in [f"{color_dim}:{i}", f"{color_dim}::{i}"]:
                                            if key_format in image_map:
                                                print(f"  {color}: {image_map[key_format]}")
                                                break
                        except Exception as e:
                            if self.verbose:
                                print(f"解析imageHashMap时出错: {e}")
                                
                            # 手动提取键值对
                            img_pairs = re.findall(r'[\'\"]([^"\']+?)[\'\"]\s*:\s*[\'\"]([^"\']+)[\'\"]', img_json)
                            for color_key, img_url in img_pairs:
                                image_map[color_key] = img_url
                                if self.verbose and len(image_map) <= 5:  # 只显示前5个
                                    print(f"  手动解析 {color_key}: {img_url}")
                    
                    # 创建小图ID到大图ID的映射
                    small_to_large_image_id_map = {}
                    
                    # 从colorImages结构中提取小图ID到大图ID的映射
                    color_images_match = re.search(r'[\'\"]colorImages[\'\"]\s*:\s*[\'\"]({.*?)[\'\"]', html_content, re.DOTALL)
                    if color_images_match:
                        try:
                            color_images_json = color_images_match.group(1)
                            fixed_json = self._fix_json_string(color_images_json)
                            color_images_data = json.loads(fixed_json)
                            
                            # 遍历所有颜色的图片数据
                            for color_key, images_list in color_images_data.items():
                                if isinstance(images_list, list):
                                    for img_item in images_list:
                                        if isinstance(img_item, dict):
                                            # 如果同时有large和hiRes字段
                                            if 'large' in img_item and 'hiRes' in img_item and img_item['hiRes']:
                                                large_url = img_item['large']
                                                hires_url = img_item['hiRes']
                                                
                                                # 提取两个URL中的图片ID
                                                large_id = self._extract_image_id(large_url)
                                                hires_id = self._extract_image_id(hires_url)
                                                
                                                if large_id and hires_id:
                                                    # 建立小图ID到大图ID的映射
                                                    small_to_large_image_id_map[large_id] = hires_id
                                                    
                                                    # 记录此映射以便调试
                                                    if self.verbose:
                                                        print(f"建立图片ID映射: {large_id} -> {hires_id}")
                        except Exception as e:
                            if self.verbose:
                                print(f"提取colorImages时出错: {e}")
                    
                    # 如果通过结构化方式无法提取，尝试直接从HTML中匹配
                    if not small_to_large_image_id_map:
                        # 查找格式为 {"large":"URL1", "hiRes":"URL2"} 的模式
                        large_hires_pattern = r'[\'\"]large[\'\"]\s*:\s*[\'\"](https://[^"\']+?)[\'\"],"hiRes":[\'\"](https://[^"\']+?)[\'\"]'
                        matches = re.findall(large_hires_pattern, html_content)
                        
                        for large_url, hires_url in matches:
                            large_id = self._extract_image_id(large_url)
                            hires_id = self._extract_image_id(hires_url)
                            
                            if large_id and hires_id:
                                small_to_large_image_id_map[large_id] = hires_id
                                
                                if self.verbose and len(small_to_large_image_id_map) <= 5:  # 只显示前5个
                                    print(f"正则匹配图片ID映射: {large_id} -> {hires_id}")
                    
                    # 提取dimensionValuesDisplayData对象
                    display_data = {}
                    display_match = re.search(r'[\'\"]dimensionValuesDisplayData[\'\"]\s*:\s*[\'\"]({.*?)[\'\"]', html_content, re.DOTALL)
                    if display_match:
                        display_json = display_match.group(1)
                        try:
                            fixed_json = self._fix_json_string(display_json)
                            display_data = json.loads(fixed_json)
                            
                            if self.verbose:
                                print(f"提取到维度值显示数据，共 {len(display_data)} 个ASIN")
                        except Exception as e:
                            if self.verbose:
                                print(f"解析dimensionValuesDisplayData时出错: {e}")
                                
                            # 手动提取键值对
                            display_pairs = re.findall(r'[\'\"]([^"\']+?)[\'\"]\s*:\s*[\'\"]([^\'\"]+)[\'\"]', display_json)
                            for asin, values in display_pairs:
                                value_list = re.findall(r'[\'\"]([^"\']+?)[\'\"]', values)
                                display_data[asin] = value_list
                                if self.verbose and len(display_data) <= 5:  # 只显示前5个
                                    print(f"  手动解析 {asin}: {value_list}")
                    
                    # 检查是否提取到了关键数据
                    if dimensions and var_values and asin_map:
                        # 生成变体数据
                        for dim_key, asin in asin_map.items():
                            # 解析维度索引
                            dim_indices = dim_key.split("_")
                            
                            # 创建变体数据
                            variant_data = {
                                'asin': asin,
                                'specs': {},
                                'stock_quantity': 50,
                                'sale_price': self.product_data.get('价格', ''),
                                'sale_price_formatted': '',
                                'image_url': ''
                            }
                            
                            # 填充规格信息
                            for i, dim_index in enumerate(dim_indices):
                                if i < len(dimensions):
                                    dim_name = dimensions[i]
                                    
                                    try:
                                        # 获取维度值
                                        idx = int(dim_index)
                                        if dim_name in var_values and idx < len(var_values[dim_name]):
                                            dim_value = var_values[dim_name][idx]
                                            
                                            # 添加规格，使用维度显示名称
                                            display_name = dimension_display_names.get(dim_name, dim_name)
                                            variant_data['specs'][dim_name] = {
                                                'value': dim_value,
                                                'key_id': dim_name,
                                                'value_id': str(idx),
                                                'display_name': display_name
                                            }
                                            
                                            # 如果是颜色维度，查找对应的图片
                                            if dim_name == "color_name" or "color" in dim_name.lower():
                                                # 1. 使用专门的颜色-图片映射方法
                                                img_url = self._map_color_name_to_image(dim_value, image_map)
                                                
                                                # 2. 如果映射方法未找到，尝试多种键格式
                                                if not img_url:
                                                    # 直接打印可用的所有imageHashMap键，便于调试
                                                    if self.verbose:
                                                        print(f"可用的imageHashMap键: {list(image_map.keys())}")
                                                    
                                                    for key_format in [f"{dim_name}:{idx}", f"{dim_name}::{idx}"]:
                                                        if key_format in image_map:
                                                            img_url = image_map[key_format]
                                                            print(f"找到图片匹配键: {key_format} -> {img_url}")
                                                            break
                                                
                                                # 3. 如果找到了图片URL，处理成高清版本
                                                if img_url:
                                                    # 提取小图ID
                                                    small_image_id = self._extract_image_id(img_url)
                                                    
                                                    # 使用小图ID到大图ID的映射
                                                    if small_image_id and small_image_id in small_to_large_image_id_map:
                                                        large_image_id = small_to_large_image_id_map[small_image_id]
                                                        hi_res_url = f"https://m.media-amazon.com/images/I/{large_image_id}._AC_SL1500_.jpg"
                                                        print(f"图片ID替换: {small_image_id} -> {large_image_id}")
                                                        variant_data['specs'][dim_name]['image_url'] = hi_res_url
                                                    else:
                                                        # 如果没有找到映射，直接替换为高清尺寸格式
                                                        hi_res_url = img_url.replace("._SS64_", "._SL1500_")
                                                        print(f"图片URL替换: {img_url} -> {hi_res_url}")
                                                    
                                                    variant_data['image_url'] = hi_res_url
                                                    # 存储图片信息到specs中，便于后续处理
                                                    variant_data['specs'][dim_name]['image_url'] = hi_res_url
                                    except Exception as e:
                                        if self.verbose:
                                            print(f"处理维度 {dim_name} 出错: {e}")
                            
                            # 输出变体信息以便调试
                            if self.verbose:
                                specs_str = ", ".join([f"{k}: {v['value']}" for k, v in variant_data['specs'].items()])
                                print(f"生成变体: ASIN={variant_data['asin']}, {specs_str}")
                            
                            variants.append(variant_data)
                        
                        if self.verbose:
                            print(f"从测试数据格式中成功提取 {len(variants)} 个变体")
                
                # 保存提取到的结构化数据，便于后续处理
                twister_data = {
                    "dimensions": dimensions,
                    "variationValues": var_values,
                    "dimensionToAsinMap": asin_map,
                    "imageHashMap": image_map,
                    "dimensionValuesDisplayData": display_data,
                    "smallToLargeImageIdMap": small_to_large_image_id_map
                }
                self.twister_data = twister_data
                
            except Exception as e:
                if self.verbose:
                    print(f"从测试数据格式提取变体时出错: {e}")
                    import traceback
                    traceback.print_exc()
                
            # 如果没有从测试数据格式提取到变体，尝试使用标准方法
            if not variants:
                # 提取并处理twister数据
                twister_data = self._load_twister_data(html_content)
                if twister_data:
                    if self.verbose:
                        # 简单显示twister数据的结构和大小
                        print("成功从脚本中提取部分变体数据")
                        for key, value in twister_data.items():
                            if isinstance(value, dict):
                                print(f"  {key}: {{{len(value)} items}}")
                            elif isinstance(value, list):
                                print(f"  {key}: [{len(value)} items]")
                            else:
                                print(f"  {key}: {str(value)[:30]}..." if len(str(value)) > 30 else f"  {key}: {value}")
                                
                    # 如果检测到TwisterData，处理变体数据
                    if 'dimensionValuesDisplayData' in twister_data or 'variationDisplayLabels' in twister_data:
                        # 这里逻辑留空，因为我们优先使用测试数据格式提取的变体
                        pass
                            
            # 返回提取到的变体信息
            return variants
            
        except Exception as e:
            if self.verbose:
                print(f"提取变体信息时出错: {e}")
                import traceback
                traceback.print_exc()
            return []
    
    def _fix_json_string(self, json_str):
        """
        修复可能有问题的JSON字符串
        """
        try:
            if not json_str:
                return "{}"
                
            # 替换JavaScript语法为标准JSON
            fixed = json_str.replace('\\\'', '\'')
            fixed = fixed.replace('\\\\"', '\\"')
            
            # 处理未闭合的JSON对象或数组
            if fixed.count('{') > fixed.count('}'):
                fixed = fixed + ('}' * (fixed.count('{') - fixed.count('}')))
            if fixed.count('[') > fixed.count(']'):
                fixed = fixed + (']' * (fixed.count('[') - fixed.count(']')))
            
            # 为未加引号的键名添加引号
            fixed = re.sub(r'([{,])\s*([a-zA-Z0-9_$]+)\s*:', r'\1"\2":', fixed)
            
            # 单引号替换为双引号(但不处理已转义的单引号)
            fixed = re.sub(r'(?<!\\)\'([^\']*?)(?<!\\)\'', r'"\1"', fixed)
            
            # 处理JavaScript布尔值和null
            fixed = fixed.replace('true', 'true').replace('false', 'false').replace('null', 'null')
            fixed = fixed.replace('True', 'true').replace('False', 'false').replace('None', 'null')
            
            # 修复尾随逗号
            fixed = re.sub(r',\s*([}\]])', r'\1', fixed)
            
            # 处理可能包含JavaScript函数的情况
            fixed = re.sub(r':\s*function\s*\([^)]*\)\s*{[^}]*}', r':"[function]"', fixed)
            
            # 处理无引号字符串值
            fixed = re.sub(r':\s*([a-zA-Z0-9_]+)([,}])', r':"\1"\2', fixed)
            
            # 处理特殊字符
            fixed = fixed.replace('\\n', '\\\\n').replace('\\r', '\\\\r').replace('\\t', '\\\\t')
            
            # 修复可能出现的JSON格式错误
            fixed = fixed.replace('""', '"').replace(',,', ',').replace('::',':')
            
            # 处理特殊的亚马逊格式
            fixed = fixed.replace('","', '","').replace('":"', '":"')
            
            # 检查是否为合法的JSON格式
            try:
                json.loads(fixed)
            except json.JSONDecodeError as e:
                if self.verbose:
                    print(f"JSON修复失败，尝试进一步修复: {e}")
                
                # 定位错误位置附近的内容
                error_pos = e.pos
                context_start = max(0, error_pos - 30)
                context_end = min(len(fixed), error_pos + 30)
                problem_area = fixed[context_start:context_end]
                
                if self.verbose:
                    print(f"问题区域: {problem_area}")
                
                # 尝试针对特定错误进行修复
                if "Expecting '\"'" in str(e):
                    # 尝试在错误位置附近添加引号
                    if error_pos < len(fixed):
                        fixed = fixed[:error_pos] + '"' + fixed[error_pos:]
                
                elif "Expecting ':'" in str(e):
                    # 尝试在错误位置附近添加冒号
                    if error_pos < len(fixed):
                        fixed = fixed[:error_pos] + ':' + fixed[error_pos:]
                
                elif "Expecting ',' or '}'" in str(e):
                    # 尝试在错误位置附近添加逗号或右花括号
                    if error_pos < len(fixed) and fixed[error_pos-1:error_pos] not in [',', '}']:
                        fixed = fixed[:error_pos] + ',' + fixed[error_pos:]
            
            return fixed
            
        except Exception as e:
            if self.verbose:
                print(f"修复JSON字符串时出错: {e}")
                traceback.print_exc()
            
            # 如果修复失败，返回空对象
            return "{}"
    
    def _enrich_variants_with_price_stock(self, variants, twister_data):
        """
        使用twister数据丰富变体的价格和库存信息，加入重试逻辑；
        先批量请求，失败部分再单 ASIN 请求。
        """
        if not variants:
            return [] if variants is None else variants
        try:
            default_price = self.product_data.get('价格', '')
            currency_symbol = self.product_data.get('货币符号', '')

            asin_list = []
            asin_to_variant = {}
            parent_asin = twister_data.get('parentAsin') or twister_data.get('currentAsin')
            if not parent_asin and variants:
                parent_asin = variants[0].get('asin')
            for v in variants:
                asin = v.get('asin')
                if asin:
                    asin_list.append(asin)
                    asin_to_variant[asin] = v

            price_map = {}
            # -------- 1) 批量 Ajax，最多 3 次 --------
            max_bulk_retry = 3
            for _ in range(max_bulk_retry):
                missing = [a for a in asin_list if a not in price_map]
                if not missing:
                    break
                bulk_res = self._fetch_variants_price_bulk(missing, parent_asin)
                price_map.update(bulk_res)
                # 轻微退避
                time.sleep(0.8)

            # -------- 2) 单 ASIN Ajax 兜底 --------
            still_missing = [a for a in asin_list if a not in price_map]
            for asin in still_missing:
                p = self._fetch_single_price(asin, parent_asin)
                if p:
                    price_map[asin] = p
                    if self.verbose:
                        print(f"  单 ASIN 获取到价格 {p} -> {asin}")

            # -------- 3) dimensionValuesData 兜底 --------
            dim_data = twister_data.get('dimensionValuesData', {})
            for asin in asin_list:
                if asin not in price_map and asin in dim_data:
                    price_val = dim_data[asin].get('price') or dim_data[asin].get('priceFormatted', '')
                    if price_val:
                        price_clean = re.sub(r'[€,£,$¥₹ ]', '', str(price_val))
                        price_map[asin] = price_clean

            # -------- 4) 应用价格映射 --------
            for v in variants:
                asin = v.get('asin')
                price_val = price_map.get(asin)
                if price_val:
                    v['sale_price'] = price_val
                    v['sale_price_formatted'] = f"{price_val}"
                else:
                    v['sale_price'] = default_price or ''
                    v['sale_price_formatted'] = f"{default_price}" if default_price else ''
                v['stock_quantity'] = 999

            # -------- 5) 回填最低价 --------
            if price_map:
                fallback = min(float(p) for p in price_map.values() if p)
                for v in variants:
                    if not v.get('sale_price'):
                        v['sale_price'] = str(fallback)
                        v['sale_price_formatted'] = str(fallback)
                        if self.verbose:
                            print(f"  回填价格 {fallback} -> {v.get('asin')}")

            if self.verbose:
                got = len(price_map)
                print(f"变体价格获取完成，成功 {got}/{len(asin_list)} 个 ASIN")
            return variants
        except Exception as e:
            if self.verbose:
                print(f"丰富变体价格信息出错: {e}")
            return variants
    
    def _extract_color_images_mapping(self, html_content=None):
        """
        简化版：直接从HTML中提取颜色名称到高清图片URL的映射，避免JSON解析
        :param html_content: HTML内容，如果为None则使用self.html_content
        :return: 颜色名称到高清图片URL的映射字典
        """
        if html_content is None:
            html_content = self.html_content
            
        color_to_image = {}
        
        if not html_content:
            return color_to_image
            
        try:
            # 直接定位colorImages部分
            start_index = html_content.find('"colorImages":{')
            if start_index == -1:
                start_index = html_content.find('"colorImages" : {')
            if start_index == -1:
                start_index = html_content.find('"colorImages" :{')
            
            if start_index == -1:
                if self.verbose:
                    print("无法在HTML中找到colorImages起始位置")
                return color_to_image
            
            # 提取colorImages部分的文本块
            color_block = ""
            bracket_count = 0
            in_quotes = False
            escape_next = False
            
            for i in range(start_index, len(html_content)):
                char = html_content[i]
                color_block += char
                
                if char == '"' and not escape_next:
                    in_quotes = not in_quotes
                elif char == '\\' and not escape_next:
                    escape_next = True
                    continue
                
                if not in_quotes:
                    if char == '{':
                        bracket_count += 1
                    elif char == '}':
                        bracket_count -= 1
                        if bracket_count == 0 and len(color_block) > 20:  # 确保我们已经找到了完整的块
                            break
                
                escape_next = False
            
            # 处理 'colorImages': { 'initial': [...]} 格式
            initial_pattern = r'[\'\"]initial[\'\"]\s*:\s*\[(.*?)\]'
            initial_match = re.search(initial_pattern, color_block, re.DOTALL)
            
            if initial_match:
                initial_content = initial_match.group(1)
                # 提取所有hiRes URL（兼容单双引号）
                hi_res_matches = re.findall(r'[\'\"]hiRes[\'\"]\s*:\s*[\'\"](https://[^\'\"]+)[\'\"]', initial_content)
                
                if self.verbose:
                    print(f"从initial格式中提取到 {len(hi_res_matches)} 个hiRes URL")
                
                # 将找到的hiRes URL添加到图片字典中，使用variant作为键
                variant_matches = re.findall(r'[\'\"]variant[\'\"]\s*:\s*[\'\"]([^\'\"]+)[\'\"]', initial_content)
                
                # 如果variant和hiRes数量匹配，建立映射关系
                if len(variant_matches) == len(hi_res_matches):
                    for i, (variant, url) in enumerate(zip(variant_matches, hi_res_matches)):
                        # 使用variant作为键，如果是MAIN则特殊处理
                        key = "main" if variant == "MAIN" else f"variant_{variant}"
                        color_to_image[key] = url
                        if self.verbose:
                            print(f"将variant '{variant}' 与图片URL配对")
                else:
                    # 如果数量不匹配，简单按顺序添加
                    for i, url in enumerate(hi_res_matches):
                        color_to_image[f"image_{i+1}"] = url
                        
                # 如果成功从initial格式提取，可以直接返回
                if color_to_image:
                    if self.verbose:
                        print(f"从initial格式成功提取 {len(color_to_image)} 个图片")
                    return color_to_image
            
            # 如果没有initial格式或提取失败，继续使用原有方法
            # 直接使用正则表达式从提取的块中获取颜色和hiRes配对
            # 模式1：寻找颜色名和hiRes高清图片的配对
            pattern1 = r'"([^"]+)"\s*:\s*\[\s*\{[^}]*?"hiRes"\s*:\s*"(https://[^"]+)"'
            color_pairs = re.findall(pattern1, color_block)
            
            if self.verbose:
                print(f"从colorImages块中找到 {len(color_pairs)} 个潜在颜色-图片配对")
            
            for color, url in color_pairs:
                # 跳过ASIN格式的键
                if len(color) == 10 and re.match(r'^[A-Z0-9]+$', color):
                    continue
                
                if url and url.startswith('https://'):
                    color_to_image[color] = url
                    if self.verbose:
                        print(f"提取到颜色 '{color}' 的高清图片URL")
            
            # 如果没有找到足够的配对，尝试另一种模式
            if len(color_to_image) < 2:
                # 模式2：直接搜索hiRes键值对，然后尝试关联到颜色
                hi_res_urls = re.findall(r'"hiRes"\s*:\s*"(https://[^"]+)"', color_block)
                color_names = re.findall(r'"([^"]+)"\s*:\s*\[', color_block)
                
                # 过滤掉ASIN格式的颜色名
                color_names = [color for color in color_names if not (len(color) == 10 and re.match(r'^[A-Z0-9]+$', color))]
                
                if self.verbose:
                    print(f"找到 {len(hi_res_urls)} 个hiRes URL和 {len(color_names)} 个颜色名")
                
                # 如果颜色和图片数量接近，可以尝试匹配
                if color_names and hi_res_urls:
                    pairs_count = min(len(color_names), len(hi_res_urls))
                    for i in range(pairs_count):
                        if color_names[i] not in color_to_image:
                            color_to_image[color_names[i]] = hi_res_urls[i]
                            if self.verbose:
                                print(f"将颜色 '{color_names[i]}' 与图片URL配对")
                                
            # 如果hiRes不足，尝试从large提取
            if len(color_to_image) < 2:
                pattern2 = r'"([^"]+)"\s*:\s*\[\s*\{[^}]*?"large"\s*:\s*"(https://[^"]+)"'
                large_pairs = re.findall(pattern2, color_block)
                
                for color, large_url in large_pairs:
                    if color not in color_to_image and large_url.startswith('https://'):
                        # 跳过ASIN格式的键
                        if len(color) == 10 and re.match(r'^[A-Z0-9]+$', color):
                            continue
                            
                        # 将large URL转换为高清格式
                        hi_res_url = re.sub(r'_AC_(?!SL1500)|_SX\d+_|_SY\d+_', '_AC_SL1500_', large_url)
                        color_to_image[color] = hi_res_url
                        if self.verbose:
                            print(f"从large URL构建颜色 '{color}' 的高清图片URL")
            
        except Exception as e:
            if self.verbose:
                print(f"提取colorImages时出错: {e}")
                traceback.print_exc()
        
        if self.verbose:
            print(f"最终提取到 {len(color_to_image)} 个颜色-图片映射")
                
        return color_to_image
    
    def _enrich_variants_with_images(self, variants, twister_data):
        """
        简化版：使用颜色到图片的直接映射为变体添加图片
        :param variants: 变体列表
        :param twister_data: twister数据
        :return: 更新后的变体列表
        """
        try:
            if not variants:
                return [] if variants is None else variants
                
            if self.verbose:
                print("开始简化版变体图片处理...")
            
            # 1. 提取颜色到图片的映射
            color_to_image = self._extract_color_images_mapping()
            if self.verbose and color_to_image:
                print(f"成功提取了{len(color_to_image)}个颜色-图片映射")
            
            # 用于追踪已经找到图片的变体ASIN
            found_images = set()
            
            # 2. 将颜色图片映射应用到变体
            variants_updated = 0
            for variant in variants:
                # 跳过已有图片的变体
                if variant.get('asin') in found_images:
                    continue
                    
                # 查找变体中的颜色信息
                for dim_name, spec_info in variant.get('specs', {}).items():
                    # 识别颜色维度
                    if 'color' in dim_name.lower() or 'farbe' in dim_name.lower() or '颜色' in dim_name:
                        color_value = None
                        if isinstance(spec_info, dict) and 'value' in spec_info:
                            color_value = spec_info['value']
                        elif isinstance(spec_info, str):
                            color_value = spec_info
                        
                        # 如果找到颜色，尝试匹配图片
                        if color_value:
                            # 1. 直接匹配
                            if color_value in color_to_image:
                                variant['image_url'] = color_to_image[color_value]
                                if isinstance(spec_info, dict):
                                    spec_info['image_url'] = color_to_image[color_value]  # 同时更新规格中的图片
                                found_images.add(variant.get('asin', ''))
                                variants_updated += 1
                                if self.verbose:
                                    print(f"为变体 {variant.get('asin', '')} 添加颜色图片: {color_value}")
                                break
                                
                            # 2. 尝试小写匹配
                            color_lower = color_value.lower()
                            color_matched = False
                            for color_name, img_url in color_to_image.items():
                                if color_name.lower() == color_lower:
                                    variant['image_url'] = img_url
                                    if isinstance(spec_info, dict):
                                        spec_info['image_url'] = img_url
                                    found_images.add(variant.get('asin', ''))
                                    variants_updated += 1
                                    if self.verbose:
                                        print(f"通过小写匹配为变体 {variant.get('asin', '')} 添加图片: {color_value} -> {color_name}")
                                    color_matched = True
                                    break
                                    
                            if color_matched:
                                break
                                
                            # 3. 尝试包含关系匹配
                            for color_name, img_url in color_to_image.items():
                                if color_name.lower() in color_lower or color_lower in color_name.lower():
                                    variant['image_url'] = img_url
                                    if isinstance(spec_info, dict):
                                        spec_info['image_url'] = img_url
                                    found_images.add(variant.get('asin', ''))
                                    variants_updated += 1
                                    if self.verbose:
                                        print(f"通过包含关系匹配为变体 {variant.get('asin', '')} 添加图片: {color_value} -> {color_name}")
                                    color_matched = True
                                    break
                            
                            if color_matched:
                                break
            
            # 3. 为仍然没有图片的变体复制其他相同颜色变体的图片
            for variant in variants:
                if variant.get('asin') not in found_images:
                    for dim_name, spec_info in variant.get('specs', {}).items():
                        if 'color' in dim_name.lower() or 'farbe' in dim_name.lower() or '颜色' in dim_name:
                            color_value = None
                            if isinstance(spec_info, dict):
                                color_value = spec_info.get('value', '')
                            elif isinstance(spec_info, str):
                                color_value = spec_info
                                
                            if color_value:
                                # 查找具有相同颜色的其他变体
                                for other_variant in variants:
                                    if other_variant.get('asin') in found_images:
                                        for other_dim, other_spec in other_variant.get('specs', {}).items():
                                            other_color = None
                                            if isinstance(other_spec, dict):
                                                other_color = other_spec.get('value', '')
                                            elif isinstance(other_spec, str):
                                                other_color = other_spec
                                                
                                            if other_color and color_value.lower() == other_color.lower():
                                                variant['image_url'] = other_variant['image_url']
                                                if isinstance(spec_info, dict):
                                                    spec_info['image_url'] = other_variant['image_url']
                                                found_images.add(variant.get('asin', ''))
                                                variants_updated += 1
                                                if self.verbose:
                                                    print(f"为变体 {variant.get('asin', '')} 从相同颜色变体复制图片")
                                                break
                                                
                                        if variant.get('asin') in found_images:
                                            break
            
            if self.verbose:
                print(f"变体图片处理完成，成功为{variants_updated}个变体添加了图片")
            
            return variants
            
        except Exception as e:
            if self.verbose:
                print(f"变体图片处理时出错: {e}")
                traceback.print_exc()
            return variants
    
    def _extract_sku(self):
        """提取产品SKU"""
        try:
            if self.verbose:
                print("提取亚马逊产品SKU...")
            
            # 方法1: 从URL中提取ASIN
            url_match = re.search(r'dp/([A-Z0-9]{10})', self.html_content)
            if url_match:
                self.product_data["SKU"] = url_match.group(1)
                if self.verbose:
                    print(f"从URL提取SKU: {self.product_data['SKU']}")
                return
            
            # 方法2: 从产品详情中提取ASIN
            asin_elem = self.soup.select_one("tr:-soup-contains('ASIN'), th:-soup-contains('ASIN')")
            if asin_elem:
                asin_text = asin_elem.parent.get_text()
                asin_match = re.search(r'ASIN\s*:?\s*([A-Z0-9]{10})', asin_text)
                if asin_match:
                    self.product_data["SKU"] = asin_match.group(1)
                    if self.verbose:
                        print(f"从产品详情提取SKU: {self.product_data['SKU']}")
                    return
            
            # 方法3: 从表单元素中提取ASIN
            for elem in self.soup.select("input[name='ASIN'], input[name='asin']"):
                if elem.has_attr("value") and re.match(r'^[A-Z0-9]{10}$', elem['value']):
                    self.product_data["SKU"] = elem['value']
                    if self.verbose:
                        print(f"从表单元素提取SKU: {self.product_data['SKU']}")
                    return
            
        except Exception as e:
            print(f"提取亚马逊SKU时出错: {e}")
    
    def _extract_ratings(self):
        """提取评分和评论数"""
        try:
            if self.verbose:
                print("提取亚马逊评分和评论数...")
            
            # 评分
            rating_elem = self.soup.select_one("#averageCustomerReviews .a-icon-alt") or \
                         self.soup.select_one(".reviewCountTextLinkedHistogram")
            
            if rating_elem:
                rating_text = rating_elem.get_text().strip()
                rating_match = re.search(r'([\d.,]+)', rating_text)
                if rating_match:
                    self.product_data["评分"] = rating_match.group(1)
                    if self.verbose:
                        print(f"找到评分: {self.product_data['评分']}")
            
            # 评论数
            reviews_elem = self.soup.select_one("#acrCustomerReviewText") or \
                          self.soup.select_one(".a-link-normal .a-size-base")
            
            if reviews_elem:
                reviews_text = reviews_elem.get_text().strip()
                reviews_match = re.search(r'([\d.,]+)', reviews_text)
                if reviews_match:
                    reviews_clean = reviews_match.group(1).replace(".", "").replace(",", "")
                    self.product_data["评论数"] = reviews_clean
                    if self.verbose:
                        print(f"找到评论数: {self.product_data['评论数']}")
            
        except Exception as e:
            print(f"提取亚马逊评分和评论数时出错: {e}")
    
    def _extract_video_url(self):
        """提取产品视频链接"""
        try:
            if self.verbose:
                print("提取亚马逊产品视频链接...")
            
            # 方法1: 从iframe标签中提取视频链接
            video_iframes = self.soup.select("iframe#ape_Detail_customer-reviews-top_Glance_iframe, iframe[id*='ape_'], iframe[src*='vse-video-slider']")
            if video_iframes:
                for iframe in video_iframes:
                    src = iframe.get('src', '')
                    if src:
                        # 尝试从iframe src获取视频ID
                        video_id_match = re.search(r'videoId=([^&]+)', src)
                        if video_id_match:
                            video_id = video_id_match.group(1)
                            # 构造mp4链接
                            mp4_url = f"https://m.media-amazon.com/videos/sm/{video_id}.mp4"
                            self.product_data["视频链接"] = mp4_url
                            if self.verbose:
                                print(f"从iframe找到视频链接: {mp4_url}")
                            return True
                        
                        # 尝试直接使用iframe src
                        if '.mp4' in src or 'm3u8' in src:
                            self.product_data["视频链接"] = src
                            if self.verbose:
                                print(f"从iframe src直接找到视频链接: {src}")
                            return True
            
            # 方法2: 从脚本中提取视频URL
            video_scripts = self.soup.find_all("script", string=re.compile("videos"))
            
            for script in video_scripts:
                if not script.string:
                    continue
                
                # 查找MP4视频链接
                mp4_match = re.search(r'https://[^\"\']+\.mp4', script.string)
                if mp4_match:
                    self.product_data["视频链接"] = mp4_match.group(0)
                    if self.verbose:
                        print(f"从脚本找到MP4视频链接: {self.product_data['视频链接']}")
                    return True
                
                # 查找m3u8视频流链接
                m3u8_match = re.search(r'(https://[^\"\']+\.m3u8)', script.string)
                if m3u8_match:
                    self.product_data["视频链接"] = m3u8_match.group(1)
                    if self.verbose:
                        print(f"从脚本找到m3u8视频链接: {self.product_data['视频链接']}")
                    return True
                
                # 查找一般视频链接
                video_match = re.search(r'\"url\"\\s*:\\s*\"(https://m.media-amazon.com/[^\"]+)\"', script.string)
                if video_match:
                    self.product_data["视频链接"] = video_match.group(1)
                    if self.verbose:
                        print(f"从脚本找到视频链接: {self.product_data['视频链接']}")
                    return True
            
            # 方法3: 从HTML中查找m3u8链接
            m3u8_match = re.search(r'(https://[^\"\']+\.m3u8)', str(self.soup))
            if m3u8_match:
                self.product_data["视频链接"] = m3u8_match.group(1)
                if self.verbose:
                    print(f"从HTML中找到m3u8链接: {self.product_data['视频链接']}")
                return True
            
            # 方法4: 查找video标签
            video_tags = self.soup.find_all("video")
            if video_tags:
                for video in video_tags:
                    src = video.get('src', '')
                    if src:
                        self.product_data["视频链接"] = src
                        if self.verbose:
                            print(f"从video标签找到视频链接: {src}")
                        return True
                    
                    # 查找子元素source标签
                    sources = video.find_all("source")
                    for source in sources:
                        src = source.get('src', '')
                        if src:
                            self.product_data["视频链接"] = src
                            if self.verbose:
                                print(f"从video>source标签找到视频链接: {src}")
                            return True
            
            if self.verbose:
                print("未找到视频链接")
            return False
            
        except Exception as e:
            if self.verbose:
                print(f"提取视频链接时出错: {e}")
            return False
    
    def _write_variants_to_file(self, file):
        """将亚马逊变体信息写入文件，以与temu_scraper.py相同的格式"""
        file.write(f"产品变体:\n")
        variants = self.product_data['产品变体']
        if isinstance(variants, list) and variants:
            # 生成combinations格式的JSON数据
            combinations_format = self._generate_combinations_format(variants)
            
            if combinations_format:
                # 第一部分：combinations_group_stringify
                spec_groups = combinations_format.get('spec_groups', {})
                if spec_groups:
                    combinations_group_stringify = json.dumps(spec_groups, ensure_ascii=False, separators=(',', ':'))
                    file.write(f"combinations_group_stringify: {combinations_group_stringify}\n\n")
                
                # 第二部分：combinations_data
                combinations = combinations_format.get('combination', [])
                if combinations:
                    combinations_data = {"combination": combinations}
                    combinations_data_json = json.dumps(combinations_data, ensure_ascii=False, separators=(',', ':'))
                    file.write(f"combinations_data: {combinations_data_json}\n")
                
                if self.verbose:
                    # 显示统计信息
                    print(f"变体JSON生成完成:")
                    print(f"- 规格组数: {len(spec_groups)}")
                    for group_id, group_info in spec_groups.items():
                        print(f"  {group_id}: {group_info['name']} ({len(group_info['values'])}个值)")
                    print(f"- 组合数: {len(combinations)}")
                    print(f"- combinations_group_stringify大小: {len(combinations_group_stringify) if spec_groups else 0} 字符")
                    print(f"- combinations_data大小: {len(combinations_data_json) if combinations else 0} 字符")
            else:
                file.write("无法生成变体JSON数据\n")
        else:
            file.write("无变体信息\n")
        
        # 新增：人类可读的变体&价格列表
        if isinstance(variants, list) and variants:
            file.write("\n===== 变体列表(价格/库存) =====\n")
            file.write("ASIN\t规格\t价格\t库存\n")
            for v in variants:
                specs_parts = []
                for spec_key, spec_info in v.get('specs', {}).items():
                    display_name = spec_info.get('display_name', spec_key)
                    specs_parts.append(f"{display_name}:{spec_info.get('value')}")
                specs_str = "; ".join(specs_parts)
                price = v.get('sale_price', '')
                qty = v.get('stock_quantity', '')
                file.write(f"{v.get('asin','')}\t{specs_str}\t{price}\t{qty}\n")
        
        # 追加变体价格API原始返回内容（仅调试）
        if hasattr(self, 'debug_variant_price_json') and self.debug_variant_price_json:
            file.write("\n===== 变体价格API原始返回(JSON文本) =====\n")
            for idx, raw_json in enumerate(self.debug_variant_price_json, 1):
                snippet = raw_json[:8000]  # 防止文件过大，截取前8k字符
                file.write(f"--- 批次 {idx} (截取前8k字符) ---\n")
                file.write(snippet + "\n\n")
    
    def _generate_combinations_format(self, variants):
        """
        生成符合combinations_group_stringify格式的JSON数据
        """
        try:
            if not variants:
                # 无变体情况：生成基础combination数据
                return self._generate_no_variant_combination()
            
            if self.verbose:
                print("生成变体规格组和组合数据...")
            
            # 收集所有规格类型和值
            spec_groups = {}
            all_specs = set()
            
            # 第一步：收集所有的规格类型
            for variant in variants:
                for spec_key in variant['specs'].keys():
                    all_specs.add(spec_key)
            
            # 第二步：为每个规格类型分配ID并收集所有可能的值
            spec_id_counter = 1
            for spec_key in all_specs:
                # 为每个规格类型分配一个ID
                spec_id = str(spec_id_counter)
                spec_id_counter += 1
                
                # 确定是否为颜色规格
                is_color = 1 if spec_key.lower() in ["color", "colour", "color_name", "颜色", "farbe"] else 0
                
                # 获取规格的显示名称（如 "Farbe" 或 "Größe"）
                display_name = ""
                for variant in variants:
                    if spec_key in variant['specs'] and 'display_name' in variant['specs'][spec_key]:
                        display_name = variant['specs'][spec_key]['display_name']
                        break
                
                if not display_name:
                    # 如果没有找到显示名称，使用默认值
                    display_name = "Color" if is_color else spec_key.replace("_", " ").title()
                
                # 创建规格组
                spec_groups[spec_id] = {
                    "is_color": is_color,
                    "is_ship_from": 0,
                    "name": display_name,
                    "values": {}
                }
                
                # 为颜色规格添加特殊字段
                if is_color:
                    spec_groups[spec_id].update({
                        "colors": [],
                        "images": [],
                        "countrycodes": []
                    })
                
                # 收集该规格类型的所有可能值
                value_id_counter = 10  # 从10开始为值分配ID
                value_to_id = {}  # 用于记录值和ID的映射
                
                # 首先收集所有的值
                for variant in variants:
                    if spec_key in variant['specs']:
                        spec_value = variant['specs'][spec_key]['value']
                        if spec_value not in value_to_id:
                            value_to_id[spec_value] = str(value_id_counter)
                            value_id_counter += 1
                
                # 填充规格组的values字段
                for value, value_id in value_to_id.items():
                    spec_groups[spec_id]["values"][value_id] = value
                
                # 如果是颜色规格，收集颜色图片
                if is_color:
                    for variant in variants:
                        if spec_key in variant['specs']:
                            spec_info = variant['specs'][spec_key]
                            spec_value = spec_info['value']
                            value_id = value_to_id.get(spec_value)
                            
                            # 如果有图片URL，添加到images列表
                            if 'image_url' in spec_info and value_id:
                                # 确保图片URL是高清版本
                                img_url = spec_info['image_url']
                                if "._SS" in img_url:
                                    img_url = img_url.replace("._SS64_", "._SL1500_")
                                
                                spec_groups[spec_id]["images"].append({
                                    "id": value_id,
                                    "url": img_url
                                })
            
            # 第三步：构建组合数据
            combinations = []
            
            for variant in variants:
                # 构建skuattr字符串
                skuattr_parts = []
                
                for spec_key, spec_info in variant['specs'].items():
                    spec_value = spec_info['value']
                    
                    # 找出该规格类型的ID
                    spec_id = None
                    for sid, group in spec_groups.items():
                        if group["name"] == spec_info.get('display_name', spec_key):
                            spec_id = sid
                            break
                    
                    if not spec_id:
                        # 如果通过display_name没找到，尝试通过spec_key查找
                        for sid, group in spec_groups.items():
                            if spec_key in group["name"].lower():
                                spec_id = sid
                                break
                    
                    if not spec_id:
                        continue
                    
                    # 找出该值的ID
                    value_id = None
                    for vid, val in spec_groups[spec_id]["values"].items():
                        if val == spec_value:
                            value_id = vid
                            break
                    
                    if value_id:
                        skuattr_parts.append(f"{spec_id}:{value_id}")
                
                if skuattr_parts:
                    # 创建组合对象
                    price = variant.get('sale_price', self.product_data['价格']) or "21.99"
                    # 确保price_formatted不包含货币符号
                    raw_price = variant.get('sale_price_formatted', '') or f"{price}"
                    # 去除所有货币符号（€、$、£等）
                    price_formatted = re.sub(r'[€$£¥₹]', '', raw_price)
                    qty = str(variant.get('stock_quantity', 30))
                    image_url = variant.get('image_url', '')
                    
                    # 确保图片URL是高清版本
                    if image_url and "._SS" in image_url:
                        image_url = image_url.replace("._SS64_", "._SL1500_")
                    
                    combination = {
                        "checked": "1",
                        "skuattr": ";".join(sorted(skuattr_parts)),
                        "original_price": price,
                        "price": price,
                        "price_formatted": price_formatted,
                        "price_comprared": "0",
                        "qty": qty,
                        "image_url": image_url
                    }
                    
                    combinations.append(combination)
                    
                    if self.verbose:
                        specs_display = []
                        for spec_key, spec_info in variant['specs'].items():
                            display_name = spec_info.get('display_name', spec_key)
                            specs_display.append(f"{display_name}: {spec_info['value']}")
                        
                        specs_str = ", ".join(specs_display)
                        print(f"生成变体组合: {specs_str} - 价格: {price_formatted} - 库存: {qty}")
            
            if self.verbose:
                print(f"共生成 {len(combinations)} 个变体组合")
                for sid, group in spec_groups.items():
                    print(f"规格组 {sid}: {group['name']} - {len(group['values'])} 个值")
            
            return {
                "spec_groups": spec_groups,
                "combination": combinations
            }
            
        except Exception as e:
            if self.verbose:
                print(f"生成变体组合格式时出错: {e}")
                traceback.print_exc()
            return self._generate_no_variant_combination()

    def _generate_no_variant_combination(self):
        """
        生成无变体情况的combination数据
        """
        if self.verbose:
            print("检测到无变体产品，生成基础combination数据")
        
        # 获取基础价格并清洗：去除货币符号，统一小数点
        raw_price = self.product_data.get('价格', '21.99') or '21.99'
        # 去除所有非数字/逗号/点字符（例如 € 、空格等）
        cleaned_price = re.sub(r'[^0-9,\.]+', '', raw_price)
        # 将逗号替换为点（欧洲价格常用逗号作小数分隔）
        if ',' in cleaned_price and '.' not in cleaned_price:
            cleaned_price = cleaned_price.replace(',', '.')
        # 如果清洗后为空，则使用默认
        base_price = cleaned_price if cleaned_price else '21.99'
        
        # 获取主图
        main_image = ""
        if self.product_data['产品图片']:
            main_image = self.product_data['产品图片'][0]
            # 确保图片URL是高清版本
            if main_image and "._SS" in main_image:
                main_image = main_image.replace("._SS64_", "._SL1500_")
        
        # 生成默认的规格组 - 使用与TEMU相同的格式
        spec_groups = {
            "2": {
                "is_color": 1,
                "is_ship_from": 0,
                "colors": [],
                "images": [{
                    "id": "10",
                    "url": main_image
                }] if main_image else [],
                "countrycodes": [],
                "name": "Color",  # 使用英语"Color"与TEMU保持一致
                "values": {
                    "10": "White"  # 使用"White"与TEMU保持一致
                }
            }
        }
        
        # 生成默认的组合 - 确保包含skuattr字段
        combination = {
            "checked": "1",
            "skuattr": "2:10",  # 关键字段：关联规格组ID和值ID
            "original_price": base_price,
            "price": base_price,
            # 确保不包含任何货币符号
            "price_formatted": re.sub(r'[€$£¥₹]', '', base_price),
            "price_comprared": "0",
            "qty": "70",  # 使用70作为默认库存，与TEMU一致
            "image_url": main_image
        }
        
        if self.verbose:
            print(f"生成默认变体组合: 价格={base_price}")
        
        return {
            "spec_groups": spec_groups,
            "combination": [combination]
        }
    
    def send_to_api(self, api_url="http://**************/wms/product/importProducts", warehouse_id="3079", source_link=""):
        """
        将提取的亚马逊产品数据发送到API接口
        :param api_url: API接口地址
        :param warehouse_id: 仓库ID
        :param source_link: 产品来源链接
        :return: 返回字典格式的结果
        """
        try:
            if self.verbose:
                print(f"准备发送亚马逊产品数据到API: {api_url}")
            
            # 检查必要的数据是否存在
            if not self.product_data['SKU']:
                print("错误: SKU信息缺失，无法发送到API")
                return {"success": False, "error": "SKU信息缺失"}
            
            if not self.product_data['标题']:
                print("错误: 产品标题缺失，无法发送到API")
                return {"success": False, "error": "产品标题缺失"}
            
            # 新增价格有效性检查——若价格无效则跳过 API 发布
            price_valid = bool(self.product_data.get('价格'))
            placeholder_prices = {'21.99', '0', '0.00', '', None}
            # 遍历变体价格
            if self.product_data.get('产品变体'):
                for _v in self.product_data['产品变体']:
                    sp = str(_v.get('sale_price', '')).strip()
                    if not sp or sp in placeholder_prices:
                        price_valid = False
                        break
            if not price_valid:
                print("错误: 未能获取有效价格信息，已取消发布到 API。")
                return {"success": False, "error": "价格缺失或无效，跳过发布", "skip_api": True}
            # ---- 以上检查结束，后续继续正常构建 POST 数据 ----
            
            # 生成变体数据
            combinations_data = {}
            combinations_group_stringify = {}
            
            # 检查是否有产品变体
            if self.product_data['产品变体']:
                combinations_format = self._generate_combinations_format(self.product_data['产品变体'])
                if combinations_format:
                    combinations_group_stringify = combinations_format.get('spec_groups', {})
                    combinations_data = {"combination": combinations_format.get('combination', [])}
            else:
                # 没有变体时，生成默认的变体数据
                if self.verbose:
                    print("产品没有变体，使用默认变体格式")
                no_variant_data = self._generate_no_variant_combination()
                if no_variant_data:
                    combinations_group_stringify = no_variant_data.get('spec_groups', {})
                    combinations_data = {"combination": no_variant_data.get('combination', [])}
                    if self.verbose:
                        print(f"生成默认变体数据: {len(combinations_data.get('combination', []))}个组合")
            
            # 构建POST数据
            post_data = {
                'alireference': self.product_data['SKU'],
                'product_name': self.product_data['标题'],
                'source_link': source_link,
                'description': self.product_data['描述'],
                'combinations_group_stringify': json.dumps(combinations_group_stringify, ensure_ascii=False, separators=(',', ':')),
                'combinations_data': json.dumps(combinations_data, ensure_ascii=False, separators=(',', ':')),
                'warehouse_id': warehouse_id,
                'category': self.product_data['产品分类'],
                'images': "|||".join(self.product_data['产品图片']),
                'video_url': self.product_data['视频链接'],
                'short_description': self.product_data.get('短描述', ''),
                'brand': self.product_data.get('品牌', ''),
                'meta_tags': self.product_data['产品分类'],
                'platform': 'amazon'  # 标识平台
            }
            
            if self.verbose:
                print("POST数据构建完成:")
                print(f"- alireference: {post_data['alireference']}")
                print(f"- product_name: {post_data['product_name'][:50]}...")
                print(f"- category: {post_data['category']}")
                print(f"- images数量: {len(self.product_data['产品图片'])}")
                print(f"- combinations_group_stringify长度: {len(post_data['combinations_group_stringify'])}")
                print(f"- combinations_data长度: {len(post_data['combinations_data'])}")
                print(f"- source_link: {post_data['source_link']}")
                print(f"- warehouse_id: {post_data['warehouse_id']}")
                print(f"- platform: {post_data['platform']}")
                print("=== 完整POST数据 ===")
                for key, value in post_data.items():
                    if key in ['combinations_group_stringify', 'combinations_data']:
                        print(f"{key}: {str(value)[:200]}...")
                    elif key == 'description':
                        print(f"{key}: {str(value)[:100]}...")
                    else:
                        print(f"{key}: {value}")
                print("=" * 50)
            
            # 发送POST请求
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': get_random_user_agent()
            }
            
            if self.verbose:
                print("正在发送POST请求...")
            
            # 保存POST数据到1.txt文件用于调试
            try:
                with open("1.txt", "w", encoding="utf-8") as f:
                    f.write("=== 发送到WMS API的POST数据 ===\n")
                    f.write(f"URL: {api_url}\n")
                    f.write(f"Headers: {headers}\n\n")
                    f.write("POST Data:\n")
                    for key, value in post_data.items():
                        f.write(f"{key}: {value}\n")
                        f.write("-" * 80 + "\n")
                if self.verbose:
                    print("POST数据已保存到 1.txt 文件")
            except Exception as e:
                if self.verbose:
                    print(f"保存POST数据到文件失败: {e}")
            
            response = requests.post(
                api_url,
                data=post_data,
                headers=headers,
                timeout=30
            )
            
            if self.verbose:
                print(f"API响应状态码: {response.status_code}")
                print(f"API响应内容: {response.text[:2000]}...")
            
            # 将API响应也追加到1.txt文件
            try:
                with open("1.txt", "a", encoding="utf-8") as f:
                    f.write("\n" + "=" * 80 + "\n")
                    f.write("=== API响应 ===\n")
                    f.write(f"状态码: {response.status_code}\n")
                    f.write(f"响应头: {dict(response.headers)}\n")
                    f.write(f"响应内容: {response.text}\n")
            except Exception as e:
                if self.verbose:
                    print(f"保存API响应到文件失败: {e}")
            
            # ---- 新的 API 响应解析逻辑 ----
            resp_text = response.text
            # 默认假设请求成功
            api_success = response.status_code == 200
            duplicate_keywords = ["SKU", "sku", "Sku"]
            duplicate_indicators = ["已存在", "重复", " exists", "duplicate"]
            error_msg = ""

            # 首先尝试解析 JSON
            try:
                resp_json = response.json()
                if isinstance(resp_json, dict):
                    # 1) 优先使用 success 字段
                    if 'success' in resp_json:
                        api_success = bool(resp_json.get('success')) or str(resp_json.get('success')).lower() in ["1", "true"]
                    # 2) 若存在 message 字段，进一步检查是否包含重复/错误提示
                    msg_field = resp_json.get('message') or resp_json.get('msg') or ""
                    if msg_field:
                        # 如果 success==False 或 message 中包含重复提示，则认为失败
                        for kw in duplicate_keywords:
                            for ind in duplicate_indicators:
                                if kw.lower() in msg_field.lower() and ind.lower() in msg_field.lower():
                                    api_success = False
                                    error_msg = msg_field
                                    break
                    # 如果 success 为 False 并且没有特定错误信息，则使用 message 作为错误
                    if not api_success and not error_msg:
                        error_msg = msg_field or "接口返回失败"
            except ValueError:
                # 不是 JSON，忽略
                pass

            if api_success:
                # 对非 JSON 响应或不含 success 字段的情况，再做关键字检测
                if not error_msg:
                    for kw in duplicate_keywords:
                        for ind in duplicate_indicators:
                            if kw.lower() in resp_text.lower() and ind.lower() in resp_text.lower():
                                api_success = False
                                error_msg = "SKU已存在"
                                break

            # 根据解析结果返回
            if api_success:
                print("✅ 亚马逊产品数据成功发送到API接口")
                return {"success": True, "status_code": response.status_code, "response": resp_text[:2000]}
            else:
                if not error_msg:
                    error_msg = resp_text[:500] if resp_text else "未知错误"
                print(f"❌ API请求失败或返回错误，状态码: {response.status_code}")
                print(f"错误信息: {error_msg}")
                return {"success": False, "status_code": response.status_code, "error": error_msg, "response": resp_text[:2000]}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {e}")
            return {"success": False, "error": f"网络请求错误: {str(e)}", "response": str(e)}
        except Exception as e:
            print(f"❌ 发送API数据时出错: {e}")
            import traceback
            if self.verbose:
                traceback.print_exc()
            return {"success": False, "error": f"发送API数据时出错: {str(e)}", "response": str(e)}
    
    def save_and_send_to_api(self, output_file="amazon_product.txt", send_to_api=True, api_url="http://**************/wms/product/importProducts"):
        """
        保存文件并发送到API接口
        :param output_file: 输出文件路径
        :param send_to_api: 是否发送到API接口
        :param api_url: API接口地址
        :return: 成功返回True，失败返回False
        """
        # 先保存到文件
        file_saved = self.save_to_file(output_file)
        
        # 如果需要，发送到API
        if send_to_api and file_saved:
            api_result = self.send_to_api(api_url)
            # 将API结果附加写入文件
            try:
                with open(output_file, 'a', encoding='utf-8') as f:
                    f.write("\n===== API发布状态 =====\n")
                    if api_result.get('success'):
                        f.write(f"Success | StatusCode: {api_result.get('status_code','')}\n")
                        # 写入响应内容（截取前1000字符防过长）
                        resp_text = api_result.get('response', '')
                        if resp_text:
                            snippet = resp_text[:1000]
                            f.write(f"Response: {snippet}\n")
                    else:
                        f.write(f"Failed  | StatusCode: {api_result.get('status_code','')} | Error: {api_result.get('error','')}\n")
                        # 如果失败但仍有原始响应内容，也写入
                        resp_text = api_result.get('response', '')
                        if resp_text:
                            snippet = resp_text[:1000]
                            f.write(f"Response: {snippet}\n")
            except Exception as e:
                if self.verbose:
                    print(f"写入API发布状态到文件时出错: {e}")
            return file_saved and api_result.get("success", False)
        
        return file_saved

    def _get_dimension_display_names(self, html_content):
        """
        提取维度的显示名称（多语言支持）
        例如：尺码的"Größe"(德语)和颜色的"Farbe"(德语)
        """
        display_names = {}
        
        # 尝试匹配variationDisplayLabels
        display_labels_pattern = r'"variationDisplayLabels"\s*:\s*(\{[^}]*\})'
        display_labels_match = re.search(display_labels_pattern, html_content, re.DOTALL)
        
        if display_labels_match:
            try:
                # 提取并修复JSON
                labels_json = self._fix_json_string(display_labels_match.group(1))
                display_labels = json.loads(labels_json)
                
                # 保存维度显示名称映射
                display_names = display_labels
                if self.verbose:
                    print(f"提取到维度显示名称: {display_names}")
            except Exception as e:
                if self.verbose:
                    print(f"解析维度显示名称时出错: {e}")
        
        return display_names

    def _map_color_name_to_image(self, color_name, image_map):
        """
        将颜色名称映射到图片URL - 增强版
        支持多种语言的颜色名称和精确的颜色匹配
        
        :param color_name: 颜色名称
        :param image_map: 图片映射字典，可以是imageHashMap或colorToImage等
        :return: 匹配的图片URL或None
        """
        if not color_name or not image_map:
            return None
            
        # 颜色名称的常见翻译，扩展版本
        color_translations = {
            "blue": ["blau", "blue", "azul", "bleu", "hell blau", "hellblau", "light blue", "dark blue", "dunkelblau"],
            "navy": ["marineblau", "navy", "navy blue", "dunkelblau", "marine", "navy-blau"],
            "red": ["rot", "red", "rojo", "rouge", "hell rot", "hellrot", "dunkelrot", "dark red", "light red"],
            "green": ["grün", "green", "verde", "vert", "hellgrün", "dunkelgrün", "light green", "dark green"],
            "black": ["schwarz", "black", "negro", "noir", "matt black", "matt schwarz", "matte black"],
            "white": ["weiß", "weiss", "white", "blanco", "blanc", "off white", "off-white", "pearl white"],
            "yellow": ["gelb", "yellow", "amarillo", "jaune", "hellgelb", "light yellow"],
            "gray": ["grau", "grey", "gray", "gris", "dunkelgrau", "hellgrau", "dark grey", "light grey"],
            "brown": ["braun", "brown", "marrón", "marron", "hellbraun", "dunkelbraun"],
            "pink": ["rosa", "pink", "rosado", "rose", "hell rosa", "dunkel rosa"],
            "orange": ["orange", "naranja", "orangerot"],
            "purple": ["lila", "purple", "violett", "violet", "púrpura"],
            "silver": ["silber", "silver", "plateado", "argent"],
            "gold": ["gold", "oro", "or", "golden"],
            "beige": ["beige", "crème", "cream", "creme"]
        }
        
        # 多语言特殊颜色术语
        special_color_terms = {
            "matt": ["matt", "matte", "matted", "matte finish"],
            "gloss": ["gloss", "glossy", "glänzend", "hochglanz"],
            "metallic": ["metallic", "metallisch", "métal"],
            "dark": ["dark", "dunkel", "oscuro", "foncé"],
            "light": ["light", "hell", "claro", "clair"],
            "bright": ["bright", "leuchtend", "brillante", "vif"]
        }
        
        # 颜色组合映射
        color_combinations = {
            "navy blue": ["navy", "blue"],
            "light blue": ["light", "blue"],
            "dark blue": ["dark", "blue"],
            "light green": ["light", "green"],
            "dark green": ["dark", "green"],
            "dark red": ["dark", "red"],
            "light red": ["light", "red"],
            "dark grey": ["dark", "gray"],
            "light grey": ["light", "gray"],
            "matt black": ["matt", "black"],
            "matte black": ["matt", "black"],
            "off white": ["off", "white"],
            "pearl white": ["pearl", "white"]
        }
        
        # 1. 首先处理输入的颜色名称
        color_name_lower = color_name.lower().strip()
        normalized_color = color_name_lower
        
        # 2. 尝试直接匹配键
        for key, url in image_map.items():
            key_lower = key.lower()
            
            # 完全匹配
            if color_name_lower == key_lower:
                if self.verbose:
                    print(f"精确颜色名称匹配: '{color_name_lower}' == '{key_lower}' -> {url}")
                return url
            
            # 键包含完整颜色名称
            if color_name_lower in key_lower:
                if self.verbose:
                    print(f"键包含颜色名称: '{key_lower}' 包含 '{color_name_lower}' -> {url}")
                return url
        
        # 3. 识别颜色组和特殊修饰词
        matched_color_group = None
        special_terms = []
        
        # 先检查是否为已知的组合颜色名称
        for combo, components in color_combinations.items():
            if combo in color_name_lower:
                special_terms = components
                if self.verbose:
                    print(f"识别到组合颜色名称: '{color_name_lower}' 包含 {components}")
                break
        
        # 如果不是组合名称，分别查找基础颜色和修饰词
        if not special_terms:
            # 识别基础颜色组
            for color_group, translations in color_translations.items():
                if any(trans == color_name_lower or trans in color_name_lower.split() for trans in translations):
                    matched_color_group = color_group
                    if self.verbose:
                        print(f"识别到颜色组: '{color_name_lower}' -> {color_group}")
                    break
            
            # 识别特殊修饰词
            for term_group, terms in special_color_terms.items():
                if any(term == color_name_lower or term in color_name_lower.split() for term in terms):
                    special_terms.append(term_group)
                    if self.verbose:
                        print(f"识别到特殊修饰词: '{color_name_lower}' 包含 {term_group}")
        
        # 4. 使用识别到的颜色组和修饰词进行匹配
        if matched_color_group or special_terms:
            for key, url in image_map.items():
                key_lower = key.lower()
                
                # 检查键是否包含颜色组的任何翻译
                if matched_color_group:
                    color_match = False
                    for trans in color_translations.get(matched_color_group, []):
                        if trans in key_lower.split():
                            color_match = True
                            break
                    
                    if not color_match:
                        continue
                
                # 检查键是否包含所有特殊修饰词
                terms_match = True
                for term_group in special_terms:
                    term_found = False
                    for term in special_color_terms.get(term_group, []):
                        if term in key_lower.split():
                            term_found = True
                            break
                    if not term_found:
                        terms_match = False
                        break
                
                if terms_match:
                    if self.verbose:
                        print(f"基于颜色组和修饰词的匹配: '{key_lower}' 匹配 {matched_color_group or ''} {special_terms} -> {url}")
                    return url
        
        # 5. 尝试更模糊的部分匹配
        for key, url in image_map.items():
            key_lower = key.lower()
            
            # 检查颜色名称的任何部分是否出现在键中
            color_parts = color_name_lower.split()
            for part in color_parts:
                if len(part) > 2 and part in key_lower:  # 只匹配长度大于2的有意义词
                    if self.verbose:
                        print(f"部分颜色名称匹配: '{key_lower}' 包含 '{part}' -> {url}")
                    return url
                    
        return None

    def _extract_short_description(self):
        """提取亚马逊产品短描述"""
        try:
            if self.verbose:
                print("提取亚马逊产品短描述...")
            
            # 定义选择器及其优先级
            selectors = [
                {'selector': 'div#productFactsDesktopExpander', 'mode': 'all', 'priority': 1},      # 高优先级
                {'selector': 'div#feature-bullets', 'mode': 'all', 'priority': 1},                 # 高优先级
                {'selector': 'div.product-facts-detail', 'mode': 'first', 'priority': 2},          # 低优先级
                {'selector': 'div#aplus_feature_div', 'mode': 'all', 'priority': 2},               # 低优先级
                {'selector': 'div#postPurchaseWhatsInTheBox_MP_feature_div', 'mode': 'all', 'priority': 1}, # 高优先级
                {'selector': 'div#productAlert_feature_div', 'mode': 'first', 'priority': 2},        # 低优先级
                {'selector': 'div#productDescription_feature_div', 'mode': 'all', 'priority': 1},   # 产品描述（高优先级）
                {'selector': 'div#importantInformation_feature_div', 'mode': 'all', 'priority': 1}, # 重要信息/警告（高优先级）
            ]
            
            output_lines = []
            found_priority_1 = False
            
            for sel in selectors:
                blocks = self.soup.select(sel['selector'])
                if not blocks:
                    continue
                
                # 如果已经找到高优先级内容，跳过低优先级
                if found_priority_1 and sel['priority'] > 1:
                    continue
                    
                if sel['mode'] == 'first':
                    block = blocks[0]
                    if sel['priority'] == 1:
                        found_priority_1 = True
 #                   tag_info = f'--- 通过标签块获取: {sel["selector"]} [1] ---'
 #                   output_lines.append(tag_info)
                    output_lines.append(block.get_text(separator='\n', strip=True))
                elif sel['mode'] == 'all':
                    for idx, block in enumerate(blocks, 1):
                        if sel['priority'] == 1:
                            found_priority_1 = True
 #                       tag_info = f'--- 通过标签块获取: {sel["selector"]} [{idx}] ---'
 #                       output_lines.append(tag_info)
                        output_lines.append(block.get_text(separator='\n', strip=True))
            
            # 替换掉 'Produktinformation' 和 'amazon'（大小写不敏感）并去重
            filtered_lines = []
            seen_content = set()
            
            for line in output_lines:
                # 大小写不敏感替换
                new_line = re.sub(r'Produktinformation', '', line, flags=re.IGNORECASE)
                new_line = re.sub(r'amazon', '', new_line, flags=re.IGNORECASE)
                new_line = re.sub(r'AmazonBasics', '', new_line, flags=re.IGNORECASE)
                
                # 去重：如果内容已存在，跳过
                if new_line in seen_content:
                    continue
                
                seen_content.add(new_line)
                filtered_lines.append(new_line)
            
            if not found_priority_1:
                filtered_lines.append('未找到短描述块')
            
            # 合并所有行为一个字符串
            short_desc = '\n'.join(filtered_lines)
            
            # 保存短描述
            if short_desc:
                self.product_data["短描述"] = short_desc
                return True
            else:
                self.product_data["短描述"] = ""
                if self.verbose:
                    print("未找到短描述内容")
                return False
                
        except Exception as e:
            print(f"提取亚马逊短描述时出错: {e}")
            self.product_data["短描述"] = ""
            return False
    
    def _final_format_short_desc(self, short_desc):
        """
        最终格式化短描述文本
        - 清理多余空格
        - 格式化列表项
        - 移除可能的重复内容
        - 确保格式统一，如果包含类似Verpackungsabmessungen的项目，则优先采用这种格式
        """
        if not short_desc:
            return ""
            
        try:
            # 移除Unicode控制字符和方向标记
            short_desc = re.sub(r'[\u200e\u200f\u200b\u200c\u200d\u2028\u2029\u202a-\u202e\u2066-\u2069]', '', short_desc)
            
            # 检查是否包含类似"Verpackungsabmessungen"的关键项
            key_terms = ["Verpackungsabmessungen", "Hersteller", "ASIN", "Kategorie", "Im Angebot von Amazon"]
            has_key_terms = any(term in short_desc for term in key_terms)
            
            # 如果包含关键项，尝试进一步提取和格式化
            if has_key_terms:
                # 检查是否需要分行
                if short_desc.count('\n') == 0 and len(short_desc.split()) > 15:
                    # 尝试将连续的关键项分开
                    for term in key_terms:
                        if term in short_desc and not short_desc.startswith(term):
                            short_desc = short_desc.replace(term, f"\n{term}")
            
            # 清理多余空格和换行
            lines = []
            for line in short_desc.split('\n'):
                line = line.strip()
                if not line:
                    continue
                
                # 清理冒号周围的空格
                line = re.sub(r'\s*:\s*', ': ', line)
                
                # 检查是否有重复的键名
                if ':' in line:
                    parts = line.split(':', 1)
                    key = parts[0].strip()
                    value = parts[1].strip()
                    
                    # 检查值中是否包含键名
                    if key in value:
                        value = value.replace(f"{key}:", "").strip()
                    
                    # 重新构建项目文本
                    line = f"{key}: {value}"
                
                lines.append(line)
                
            # 处理常见的亚马逊短描述格式
            formatted_lines = []
            for line in lines:
                # 如果是键值对格式，保持原样
                if ':' in line and not line.startswith('•'):
                    formatted_lines.append(line)
                # 如果已经有项目符号，保持原样
                elif line.startswith('•'):
                    formatted_lines.append(line)
                # 否则添加项目符号
                else:
                    formatted_lines.append('• ' + line)
            
            # 移除可能的重复行
            unique_lines = []
            for line in formatted_lines:
                if line not in unique_lines:
                    unique_lines.append(line)
            
            # 合并为最终文本
            final_desc = '\n'.join(unique_lines)
            
            if self.verbose:
                print(f"短描述格式化完成，最终长度: {len(final_desc)}")
                
            return final_desc
            
        except Exception as e:
            if self.verbose:
                print(f"格式化短描述时出错: {e}")
            return short_desc  # 如果出错，返回原始文本
    
    def save_to_file(self, output_file="amazon_product.txt"):
        """
        将提取的信息保存到文本文件
        :param output_file: 输出文件路径
        """
        try:
            if self.verbose:
                print(f"保存信息到文本文件: {output_file}")
                
            with open(output_file, 'w', encoding='utf-8') as file:
                file.write(f"{self.get_platform_name()}产品信息提取结果\n")
                file.write(f"=" * 50 + "\n\n")
                
                file.write(f"产品标题: {self.product_data['标题']}\n\n")
                
                # 直接使用描述，不再使用短描述作为替代
                file.write(f"产品描述: {self.product_data['描述']}\n\n")
                
                # 添加短描述
                file.write(f"短描述: {self.product_data.get('短描述', '')}\n\n")
                
                file.write(f"产品价格: {self.product_data['价格']}\n\n")
                file.write(f"产品SKU: {self.product_data['SKU']}\n\n")
                
                file.write(f"产品评分: {self.product_data['评分']}\n")
                file.write(f"评论数量: {self.product_data['评论数']}\n")
                file.write(f"销量信息: {self.product_data['销量信息']}\n\n")
                
                # 产品分类 - 使用|||分割符
                file.write(f"产品分类: {self.product_data['产品分类']}\n\n")
                
                # 产品图片链接 - 使用单行|||分隔符
                if self.product_data['产品图片']:
                    images_str = "|||".join(self.product_data['产品图片'])
                    file.write(f"产品图片链接: {images_str}\n\n")
                else:
                    file.write(f"产品图片链接: \n\n")
                
                # 产品视频链接
                file.write(f"产品视频链接: {self.product_data['视频链接']}\n\n")
                
                # 产品变体信息
                self._write_variants_to_file(file)
                
                print(f"{self.get_platform_name()}产品信息已保存到 {output_file}")
                return True
        except Exception as e:
            print(f"保存文件时出错: {e}")
            return False

    def save_amazon_product_info(self):
        """
        专门为Amazon产品保存信息到amazon_product.txt文件
        """
        return self.save_to_file("amazon_product.txt")

    def _optimize_html_for_shopify(self, html_content):
        """
        优化HTML结构，使其更适合Shopify发布
        - 简化嵌套结构
        - 移除多余的DIV标签
        - 保留基本的语义结构
        - 清理类名和ID
        - 移除grey-pixel.gif占位图片
        - 再次检查并移除任何表单元素
        """
        if not html_content:
            return ""
        
        try:
            # 首先再次检查表单元素
            has_forms, form_count = self._contains_form_elements(html_content)
            if has_forms:
                if self.verbose:
                    print(f"优化前检测到表单元素，数量: {form_count}，将进行额外过滤")
                # 再次应用过滤器移除表单元素
                html_content = self._filter_irrelevant_content(html_content)
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 1. 移除所有类名和ID (除了特定的有用类)
            useful_classes = ['product-description', 'size-chart', 'a-section', 'a-spacing-small', 
                             'a-divider', 'a-divider-section', 'a-divider-inner', 'default']
            useful_ids = ['productDescription', 'productDescription_feature_div', 'important-information']
            
            for tag in soup.find_all(True):
                if tag.has_attr('class'):
                    # 检查是否有需要保留的class
                    keep_classes = [cls for cls in tag['class'] if cls in useful_classes]
                    if keep_classes:
                        tag['class'] = keep_classes
                    else:
                        del tag['class']
                
                # 保留有用的ID，移除其他ID
                if tag.has_attr('id'):
                    if tag['id'] not in useful_ids:
                        del tag['id']
                
                # 移除所有data-*属性
                data_attrs = [attr for attr in tag.attrs if attr.startswith('data-')]
                for attr in data_attrs:
                    del tag[attr]
            
            # 2. 保留原始HTML结构，不进行过多的简化
            # 只移除空元素
            for tag in soup.find_all():
                if not tag.contents and tag.name not in ['br', 'hr', 'img', 'input']:
                    tag.decompose()
                elif tag.name in ['p', 'span', 'div'] and not tag.get_text(strip=True) and not tag.find_all(['img', 'br', 'hr']):
                    tag.decompose()
            
            # 3. 处理图片 - 添加响应式类并移除grey-pixel.gif占位图片
            for img in soup.find_all('img'):
                # 移除grey-pixel.gif占位图片，但保留周围内容
                if img.has_attr('src') and 'grey-pixel.gif' in img['src']:
                    img.decompose()
                    continue
                
                if img.has_attr('src') and img['src'].startswith('http'):
                    # 确保图片有alt属性
                    if not img.has_attr('alt') or not img['alt']:
                        img['alt'] = "产品图片"
                    
                    # 添加响应式类
                    if img.has_attr('class'):
                        img['class'].append('img-responsive')
                    else:
                        img['class'] = ['img-responsive']
            
            # 4. 最终清理 - 规范化HTML
            html = str(soup)
            
            # 5. 不再添加额外的包装容器，保留原始结构
            return html
            
        except Exception as e:
            if self.verbose:
                print(f"优化HTML结构时出错: {e}")
                traceback.print_exc()
            return html_content

    def _filter_irrelevant_content(self, html_content):
        """
        过滤与产品描述无关的内容
        - 移除包含产品链接的表格
        - 移除产品对比表格
        - 移除纯品牌宣传内容
        - 移除与产品描述无关的其他aplus模块
        - 检测并移除重复的A+内容模块
        - 移除包含grey-pixel.gif的占位图片
        - 移除productDetails_db_sections内容
        - 移除包含<input name="的内容及其父容器
        - 移除带有表单特性的div容器
        - 移除购买按钮和相关互动元素
        - 移除包含<a href="/dp/"产品链接的内容块
        """
        if not html_content:
            return html_content
            
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 1. 过滤产品链接表格 - 通常包含多个产品和"查看价格"等链接
            product_tables = soup.find_all('table', class_=lambda c: c and ('comparison_table' in c or 'product-table' in c))
            for table in product_tables:
                if self.verbose:
                    print(f"移除产品链接表格，长度: {len(str(table))}")
                table.decompose()
            
            # 2. 过滤品牌宣传内容 - 通常是纯品牌介绍，与产品无关
            brand_divs = soup.find_all('div', class_=lambda c: c and ('brand-story' in c or 'brand-description' in c))
            for div in brand_divs:
                if self.verbose:
                    print(f"移除品牌宣传内容，长度: {len(str(div))}")
                div.decompose()
            
            # 3. 过滤重复的A+内容模块
            aplus_divs = soup.find_all('div', class_=lambda c: c and 'aplus-module' in str(c))
            seen_content = set()
            for div in aplus_divs:
                content = div.get_text().strip()
                if len(content) > 100:  # 只比较有意义的内容
                    content_hash = hash(content[:300])  # 使用前300个字符作为指纹
                    if content_hash in seen_content:
                        if self.verbose:
                            print(f"移除重复的A+内容模块，长度: {len(str(div))}")
                        div.decompose()
                    else:
                        seen_content.add(content_hash)
            
            # 4. 过滤包含grey-pixel.gif的占位图片
            grey_pixels = soup.find_all('img', src=lambda s: s and 'grey-pixel.gif' in s)
            for img in grey_pixels:
                if self.verbose:
                    print(f"移除grey-pixel占位图片")
                img.decompose()  # 只移除图片，保留周围内容
            
            # 5. 移除productDetails_db_sections内容
            db_sections = soup.find_all('div', id='productDetails_db_sections')
            if db_sections:
                for section in db_sections:
                    if self.verbose:
                        print(f"移除productDetails_db_sections内容，长度: {len(str(section))}")
                    section.decompose()
            
            # 6. 同时查找带有部分ID名的元素 (处理动态ID情况)
            partial_db_sections = soup.select("div[id*='productDetails_db_sections']")
            for section in partial_db_sections:
                if section not in db_sections:  # 避免重复处理
                    if self.verbose:
                        print(f"移除动态ID的productDetails_db_sections内容，长度: {len(str(section))}")
                    section.decompose()
            
            # 7. 移除所有表单元素及其父容器
            form_elements = soup.find_all('form')
            for form in form_elements:
                if self.verbose:
                    print(f"移除表单元素，长度: {len(str(form))}")
                form.decompose()
            
            # 8. 更全面地移除包含<input name="的内容
            # 8.1 首先找到所有input元素
            input_elements = soup.find_all('input')
            for input_elem in input_elements:
                if input_elem.has_attr('name'):
                    # 8.2 查找多层父容器，移除整个相关块
                    parent_container = None
                    parent = input_elem.parent
                    
                    # 向上查找最多3层，寻找合适的容器
                    for _ in range(3):
                        if parent and (parent.name == 'div' or parent.name == 'form' or parent.name == 'fieldset'):
                            # 如果找到表单相关的父容器，选择它
                            if parent.name == 'form' or parent.has_attr('class') and any(c in str(parent['class']).lower() for c in ['form', 'input', 'submit', 'purchase']):
                                parent_container = parent
                                break
                        if parent:
                            parent = parent.parent
                        else:
                            break
                    
                    # 如果找到了合适的父容器，移除整个容器
                    if parent_container:
                        if self.verbose:
                            print(f"移除包含<input name=\"{input_elem.get('name', '')}\"的父容器 ({parent_container.name})，长度: {len(str(parent_container))}")
                        parent_container.decompose()
                    else:
                        # 找不到合适的父容器时，移除input元素的直接父元素
                        direct_parent = input_elem.parent
                        if direct_parent and direct_parent.name != 'body' and direct_parent.name != 'html':
                            if self.verbose:
                                print(f"移除<input name=\"{input_elem.get('name', '')}\"的直接父元素 ({direct_parent.name})，长度: {len(str(direct_parent))}")
                            direct_parent.decompose()
                        else:
                            # 如果无法安全地移除父元素，只移除input元素本身
                            if self.verbose:
                                print(f"移除<input name=\"{input_elem.get('name', '')}\"元素本身")
                            input_elem.decompose()
            
            # 9. 查找可能包含表单特性的div容器
            form_like_divs = soup.find_all('div', attrs={
                'class': lambda c: c and any(form_class in str(c).lower() for form_class in 
                          ['form', 'input', 'submit', 'purchase', 'buy', 'cart', 'order', 'checkbox'])
            })
            
            for div in form_like_divs:
                # 如果确认是表单相关的div（含有input、button或特定class）
                if div.find('input') or div.find('button') or div.find('select'):
                    if self.verbose:
                        print(f"移除表单特性的div容器，长度: {len(str(div))}")
                    div.decompose()
            
            # 10. 移除购买按钮和相关互动元素
            buy_buttons = soup.find_all(['button', 'a'], attrs={
                'class': lambda c: c and any(btn_class in str(c).lower() for btn_class in 
                           ['buy', 'cart', 'basket', 'purchase', 'add-to-cart', 'warenkorb'])
            })
            
            for btn in buy_buttons:
                if self.verbose:
                    print(f"移除购买按钮元素")
                btn.decompose()
                
            # 10.1 移除包含<a href="/dp/"产品链接的内容块
            # 首先查找所有包含/dp/链接的a标签
            product_links = soup.find_all('a', href=lambda h: h and '/dp/' in h)
            
            if product_links and self.verbose:
                print(f"找到{len(product_links)}个产品链接")
                
            for link in product_links:
                # 尝试找到合适的父容器来移除
                parent_container = None
                current = link
                
                # 向上查找最多3层，寻找可能的产品推荐容器
                for _ in range(3):
                    if not current or current.name == 'body':
                        break
                        
                    parent = current.parent
                    if not parent:
                        break
                        
                    # 检查是否是产品推荐容器
                    if parent.name == 'div' and (
                        (parent.has_attr('class') and any(c in str(parent.get('class', '')) for c in 
                                                        ['similar', 'recommendation', 'carousel', 'product', 'comparison'])) or
                        len(parent.find_all('a', href=lambda h: h and '/dp/' in h)) > 1  # 如果包含多个产品链接
                    ):
                        parent_container = parent
                        break
                        
                    current = parent
                
                # 如果找到了合适的父容器，移除整个容器
                if parent_container:
                    if self.verbose:
                        print(f"移除包含产品链接的父容器，长度: {len(str(parent_container))}")
                    parent_container.decompose()
                else:
                    # 如果只是单个产品链接，检查是否有明显的产品卡片特征
                    card_parent = link.find_parent(['div', 'li', 'span'], class_=lambda c: c and any(
                        card_class in str(c).lower() for card_class in 
                        ['card', 'item', 'product', 'tile', 'box']
                    ))
                    
                    if card_parent:
                        if self.verbose:
                            print(f"移除产品卡片元素，长度: {len(str(card_parent))}")
                        card_parent.decompose()
                    else:
                        # 如果找不到合适的父容器，尝试检查链接文本是否是产品推荐
                        link_text = link.get_text().strip().lower()
                        is_recommendation = any(rec_text in link_text for rec_text in 
                                              ['also bought', 'also viewed', 'similar', 'related', 
                                               'compare with', 'sponsored', 'recommendation'])
                        
                        if is_recommendation or len(link_text) < 20:  # 短文本链接可能是产品推荐
                            if self.verbose:
                                print(f"移除产品推荐链接: {link_text}")
                            link.decompose()
            
            # 11. 使用字符串搜索处理可能被解析为文本的<input name="标签
            html_str = str(soup)
            
            # 确认是否仍有未处理的input标签
            if '<input name="' in html_str:
                # 查找所有块级容器，检查其中是否包含<input name="
                potential_containers = soup.find_all(['div', 'section', 'aside', 'fieldset'])
                
                for container in potential_containers:
                    container_html = str(container)
                    if '<input name="' in container_html:
                        # 计算容器的嵌套深度
                        depth = 0
                        parent = container.parent
                        while parent and parent.name != 'body' and depth < 3:
                            parent = parent.parent
                            depth += 1
                        
                        # 根据嵌套深度决定是否移除整个容器
                        if depth >= 2:  # 如果嵌套较深，说明可能是子表单，可以安全移除
                            if self.verbose:
                                print(f"通过字符串搜索发现并移除包含<input name=\"的容器，长度: {len(container_html)}")
                            container.decompose()
            
            # 12. 最终检查：如果仍有input标签，使用更激进的字符串替换方法
            html_str = str(soup)
            if '<input name="' in html_str:
                if self.verbose:
                    print("使用字符串替换方法移除剩余的<input name=\"标签")
                
                # 拆分HTML字符串，并在每个<input name="处进行处理
                parts = html_str.split('<input name="')
                clean_parts = [parts[0]]  # 保留第一部分
                
                for i in range(1, len(parts)):
                    # 找到当前部分的闭合标签位置
                    closing_pos = parts[i].find('>')
                    if closing_pos != -1:
                        # 从闭合标签后开始保留
                        clean_parts.append(parts[i][closing_pos+1:])
                    else:
                        # 如果找不到闭合标签，进行保守处理
                        end_pos = parts[i].find('<')
                        if end_pos != -1:
                            clean_parts.append(parts[i][end_pos:])
                        else:
                            clean_parts.append(parts[i])
                
                # 重新构建HTML
                cleaned_html = ''.join(clean_parts)
                
                # 将清理后的HTML转回BeautifulSoup对象
                soup = BeautifulSoup(cleaned_html, 'html.parser')
            
            # 13. 字符串级别过滤产品链接
            html_str = str(soup)
            if '<a href="/dp/' in html_str or '<a href="https://www.amazon' in html_str:
                if self.verbose:
                    print("使用字符串替换方法移除剩余的产品链接")
                
                # 处理内部产品链接 <a href="/dp/
                if '<a href="/dp/' in html_str:
                    # 找到所有内部产品链接的位置
                    link_positions = []
                    start_pos = 0
                    while True:
                        pos = html_str.find('<a href="/dp/', start_pos)
                        if pos == -1:
                            break
                        link_positions.append(pos)
                        start_pos = pos + 1
                    
                    # 从后向前处理，避免位置变化
                    for pos in reversed(link_positions):
                        # 找到链接的闭合标签
                        link_end = html_str.find('</a>', pos)
                        if link_end != -1:
                            # 检查是否是产品推荐链接
                            link_text = html_str[pos:link_end]
                            is_recommendation = any(rec_text in link_text.lower() for rec_text in 
                                                  ['also bought', 'also viewed', 'similar', 'related', 
                                                   'compare with', 'sponsored', 'recommendation'])
                            
                            if is_recommendation or len(link_text) < 150:  # 短链接可能是产品推荐
                                # 移除整个链接
                                html_str = html_str[:pos] + html_str[link_end+4:]
                                if self.verbose:
                                    print(f"移除内部产品链接: {link_text[:50]}...")
                
                # 处理外部产品链接 <a href="https://www.amazon
                if '<a href="https://www.amazon' in html_str:
                    # 找到所有外部产品链接的位置
                    ext_link_positions = []
                    start_pos = 0
                    while True:
                        pos = html_str.find('<a href="https://www.amazon', start_pos)
                        if pos == -1:
                            break
                        ext_link_positions.append(pos)
                        start_pos = pos + 1
                    
                    # 从后向前处理，避免位置变化
                    for pos in reversed(ext_link_positions):
                        # 找到链接的闭合标签
                        link_end = html_str.find('</a>', pos)
                        if link_end != -1:
                            # 检查链接是否包含/dp/
                            link_text = html_str[pos:link_end]
                            if '/dp/' in link_text or '/gp/' in link_text:
                                # 移除整个链接
                                html_str = html_str[:pos] + html_str[link_end+4:]
                                if self.verbose:
                                    print(f"移除外部产品链接: {link_text[:50]}...")
                
                # 将清理后的HTML转回BeautifulSoup对象
                soup = BeautifulSoup(html_str, 'html.parser')
            
            # 返回过滤后的HTML
            return str(soup)
            
        except Exception as e:
            if self.verbose:
                print(f"过滤不相关内容时出错: {e}")
                traceback.print_exc()
            return html_content

    def _calculate_text_similarity(self, text1, text2):
        """
        计算两段文本的相似度
        使用更精确的相似度算法，而不是简单的长度比较
        """
        # 如果任一文本为空，返回0相似度
        if not text1 or not text2:
            return 0
            
        # 规范化文本(去除多余空格、转小写)
        text1 = ' '.join(text1.lower().split())
        text2 = ' '.join(text2.lower().split())
        
        # 如果完全相同，返回1.0
        if text1 == text2:
            return 1.0
        
        # 计算较长文本和较短文本
        if len(text1) < len(text2):
            shorter, longer = text1, text2
        else:
            shorter, longer = text2, text1
        
        # 如果较短文本是较长文本的一部分，计算覆盖率
        if shorter in longer:
            return len(shorter) / len(longer)
        
        # 计算共同子串的长度
        # 这是一个简化的相似度计算，实际项目中可以使用更复杂的算法
        # 如编辑距离、Jaccard相似度等
        
        # 分词比较(按空格分词)
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        # 计算Jaccard相似度: 交集大小/并集大小
        common_words = words1.intersection(words2)
        all_words = words1.union(words2)
        
        if not all_words:  # 防止除零错误
            return 0
            
        return len(common_words) / len(all_words)

    def _contains_form_elements(self, html_content):
        """
        检查HTML内容是否包含表单元素
        返回布尔值和找到的表单元素数量
        """
        try:
            if not html_content:
                return False, 0
                
            # 使用字符串检查更快速判断
            form_indicators = ['<form', '<input', '<button', '<select', '<option', '<textarea', 
                              '<fieldset', 'type="submit"', 'type="button"', 'type="checkbox"',
                              'type="radio"', '<input name="']
            
            # 快速字符串检查
            has_indicators = any(indicator in html_content for indicator in form_indicators)
            if not has_indicators:
                return False, 0
            
            # 如果通过了快速检查，使用BeautifulSoup进行精确检查
            soup = BeautifulSoup(html_content, 'html.parser')
            form_elements = soup.find_all(['form', 'input', 'button', 'select', 'textarea', 'fieldset'])
            
            # 查找可能的表单相关属性
            elements_with_form_attrs = soup.find_all(attrs={
                'type': ['submit', 'button', 'checkbox', 'radio', 'password', 'file'],
                'name': True,
                'action': True,
                'method': True,
                'onsubmit': True
            })
            
            # 合并并去重
            all_elements = list(set(form_elements + elements_with_form_attrs))
            count = len(all_elements)
            
            return count > 0, count
            
        except Exception as e:
            if self.verbose:
                print(f"检查表单元素时出错: {e}")
            return False, 0

    def _contains_product_links(self, html_content):
        """
        检查HTML内容是否包含亚马逊产品链接
        返回布尔值和找到的产品链接数量
        """
        try:
            if not html_content:
                return False, 0
                
            # 使用字符串检查快速判断
            product_link_indicators = [
                '<a href="/dp/', 
                '<a href="https://www.amazon', 
                'amazon.com/dp/', 
                'amazon.de/dp/',
                'amazon.co.uk/dp/',
                'amazon.fr/dp/',
                'amazon.it/dp/',
                'amazon.es/dp/'
            ]
            
            # 快速字符串检查
            has_indicators = any(indicator in html_content for indicator in product_link_indicators)
            if not has_indicators:
                return False, 0
            
            # 如果通过了快速检查，使用BeautifulSoup进行精确检查
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有产品链接
            product_links = soup.find_all('a', href=lambda h: h and (
                '/dp/' in h or 
                (h.startswith('https://www.amazon') and ('/dp/' in h or '/gp/' in h))
            ))
            
            count = len(product_links)
            
            # 如果数量大于阈值，可能是产品推荐区域
            is_product_recommendation = count > 1
            
            # 如果只有一个链接，检查是否是产品推荐
            if count == 1 and not is_product_recommendation:
                link = product_links[0]
                link_text = link.get_text().strip().lower()
                is_product_recommendation = any(rec_text in link_text for rec_text in 
                                              ['also bought', 'also viewed', 'similar', 'related', 
                                               'compare with', 'sponsored', 'recommendation'])
            
            return is_product_recommendation, count
            
        except Exception as e:
            if self.verbose:
                print(f"检查产品链接时出错: {e}")
            return False, 0

    def _extract_price(self):
        """提取产品价格"""
        try:
            if self.verbose:
                print("提取亚马逊产品价格...")
            
            # 方法1: 从主要价格元素中提取
            price_elem = self.soup.select_one(".a-price .a-offscreen") or \
                        self.soup.select_one("#priceblock_ourprice") or \
                        self.soup.select_one("#priceblock_dealprice") or \
                        self.soup.select_one(".a-price-current .a-offscreen") or \
                        self.soup.select_one(".a-price-whole")
            
            if price_elem:
                price_text = price_elem.get_text().strip()
                # 清理价格文本，只保留数字、小数点和逗号
                price_with_currency = re.sub(r'[^\d.,€$£¥₹]', '', price_text)
                # 进一步清理，去除所有货币符号
                price_clean = re.sub(r'[€$£¥₹]', '', price_with_currency)
                if price_clean:
                    self.product_data["价格"] = price_clean
                    if self.verbose:
                        print(f"找到主要价格: {self.product_data['价格']}")
                    return
            
            # 方法2: 从变体价格中提取（如果已有变体数据）
            if self.product_data.get('产品变体'):
                for variant in self.product_data['产品变体']:
                    if variant.get('price') and variant['price'] != 'N/A':
                        self.product_data["价格"] = variant['price']
                        if self.verbose:
                            print(f"从变体数据提取价格: {self.product_data['价格']}")
                        return
            
            # 方法3: 从价格范围中提取
            price_range_elem = self.soup.select_one(".a-price-range")
            if price_range_elem:
                price_text = price_range_elem.get_text().strip()
                # 提取价格范围中的第一个价格
                price_match = re.search(r'([\d.,]+)', price_text)
                if price_match:
                    self.product_data["价格"] = price_match.group(1)
                    if self.verbose:
                        print(f"从价格范围提取价格: {self.product_data['价格']}")
                    return
            
            # 方法4: 从JSON数据中提取价格
            scripts = self.soup.find_all("script", string=re.compile("price"))
            for script in scripts:
                if not script.string:
                    continue
                
                # 查找价格相关的JSON数据
                price_match = re.search(r'"price"[:\s]*"?([0-9.,]+)"?', script.string)
                if price_match:
                    price_value = price_match.group(1)
                    self.product_data["价格"] = price_value
                    if self.verbose:
                        print(f"从脚本数据提取价格: {self.product_data['价格']}")
                    return
            
            # 方法5: 备用价格选择器
            backup_selectors = [
                "#price_inside_buybox",
                ".a-price-symbol + .a-price-whole",
                "[data-asin-price]",
                ".sx-price .sx-price-large"
            ]
            
            for selector in backup_selectors:
                elem = self.soup.select_one(selector)
                if elem:
                    price_text = elem.get_text().strip()
                    # 清理价格文本，只保留数字、小数点和逗号
                    price_with_currency = re.sub(r'[^\d.,€$£¥₹]', '', price_text)
                    # 进一步清理，去除所有货币符号
                    price_clean = re.sub(r'[€$£¥₹]', '', price_with_currency)
                    if price_clean:
                        self.product_data["价格"] = price_clean
                        if self.verbose:
                            print(f"从备用选择器提取价格: {self.product_data['价格']}")
                        return
            
            if self.verbose:
                print("未找到价格信息")
                
        except Exception as e:
            print(f"提取亚马逊价格时出错: {e}")

    def _fetch_variants_price_bulk(self, asin_list, parent_asin):
        """
        批量获取多个变体的价格信息
        通过/gp/product/ajax接口一次性获取多个变体的价格
        
        :param asin_list: ASIN列表
        :param parent_asin: 父ASIN
        :return: {asin: price} 格式的价格字典
        """
        if not asin_list:
            return {}
            
        # 每次请求的最大ASIN数量
        BATCH_SIZE = 15
        
        price_map = {}
        # 按批次处理ASIN列表
        for i in range(0, len(asin_list), BATCH_SIZE):
            batch = asin_list[i:i+BATCH_SIZE]
            
            if self.verbose:
                print(f"批量获取变体价格: {len(batch)}个ASIN")

            # 使用动态检测的域名构建URL
            domain = self.amazon_domain or "amazon.de"
            url = f"https://www.{domain}/gp/product/ajax"
            params = {
                "isDimensionSlotsAjax": "1",
                "vs": "1",
                "experienceId": "twisterDimensionSlotsDefault",
                "asin": batch[0],  # 当前ASIN
                "parentAsin": parent_asin,
                "asinList": ",".join(batch),
                "deviceType": "web",
                "deviceOs": "unrecognized",
                "landingAsin": batch[0],
                "showFancyPrice": "false",
                "twisterFlavor": "twisterPlusDesktopConfigurator"
            }
            
            # 有些页面可能包含这些信息，如果有就传递
            if hasattr(self, 'product_type') and self.product_type:
                params["productTypeDefinition"] = self.product_type
            if hasattr(self, 'product_group_id') and self.product_group_id:
                params["productGroupId"] = self.product_group_id
                
            headers = {
                **self.headers,
                "x-requested-with": "XMLHttpRequest",
                "accept": "text/html,*/*",
                "referer": f"https://www.{domain}/"
            }
            
            # 打印完整请求URL和参数（调试用）
            import urllib.parse
            full_url = url + "?" + urllib.parse.urlencode(params)
            if self.verbose:
                print(f"请求变体价格URL: {full_url}")
                print(f"请求Headers: {json.dumps({k: v for k, v in headers.items() if k in ['user-agent', 'accept', 'x-requested-with']}, indent=2)}")
                print(f"使用代理: {self.proxy}")
            
            # 尝试3次，指数退避策略
            max_retries = 3
            for retry in range(max_retries):
                try:
                    r = self.session.get(url, params=params, headers=headers,
                                        proxies=self.proxy, timeout=10)
                    
                    if r.status_code == 200:
                        if self.verbose:
                            print(f"请求成功，响应长度: {len(r.text)} 字节")
                            print(f"响应前100字符: {r.text[:100]}...")
                        
                        # 解析返回的分段JSON
                        parts = [p.strip() for p in r.text.split("&&&") if p.strip()]
                        # 保存原始批量JSON文本供调试写文件
                        self.debug_variant_price_json.append(r.text)
                        if self.verbose:
                            print(f"解析到 {len(parts)} 个JSON片段")
                            
                        for p in parts:
                            try:
                                obj = json.loads(p)
                                asin = obj["ASIN"]
                                price_json = obj["Value"]["content"]["twisterSlotJson"]
                                if "price" in price_json:
                                    price_str = price_json["price"]
                                    # 处理价格：可能是字符串或已经是数字
                                    if isinstance(price_str, str):
                                        price = float(price_str.replace(",", "."))
                                    else:
                                        price = float(price_str)  # 已经是数字类型
                                    price_map[asin] = price
                                    if self.verbose:
                                        print(f"  获取价格成功 ASIN={asin}, 价格={price}")
                            except (KeyError, ValueError, json.JSONDecodeError, AttributeError) as e:
                                if self.verbose:
                                    print(f"  解析变体价格数据失败: {str(e)}")
                                    print(f"  JSON片段: {p[:200]}...")
                                continue
                        
                        # 成功获取价格，跳出重试循环
                        break
                    else:
                        wait_time = 2 ** retry  # 指数退避
                        if self.verbose:
                            print(f"  批量获取变体价格失败，状态码: {r.status_code}，{wait_time}秒后重试...")
                            print(f"  响应内容: {r.text[:200]}...")
                        time.sleep(wait_time)
                except Exception as e:
                    wait_time = 2 ** retry
                    if self.verbose:
                        print(f"  批量获取变体价格出错: {type(e).__name__}: {str(e)}，{wait_time}秒后重试...")
                        import traceback
                        traceback.print_exc()
                    time.sleep(wait_time)
            
            # 请求间隔，避免被反爬
            time.sleep(random.uniform(1, 3))
                
        return price_map

    def _extract_image_id(self, image_url):
        """
        增强版：从图片URL中提取亚马逊图片ID
        例如从 https://m.media-amazon.com/images/I/51ZxrcCKRgL._AC_.jpg 提取 51ZxrcCKRgL
        支持多种格式的图片URL
        """
        try:
            if not image_url or not isinstance(image_url, str):
                return None
                
            # 使用更精确的正则表达式提取图片ID
            # 主要匹配格式: /images/I/字母数字组合.[格式后缀]
            match = re.search(r'/images/I/([A-Za-z0-9]+)[._]', image_url)
            if match:
                return match.group(1)
            
            # 尝试其他可能的格式
            alt_match = re.search(r'/images/([A-Za-z0-9]+)-', image_url)
            if alt_match:
                return alt_match.group(1)
                
            return None
        except Exception as e:
            if self.verbose:
                print(f"提取图片ID时出错: {e}")
            return None
    
    def _get_hires_from_image_id(self, image_id, known_hires_urls=None):
        """
        增强版：尝试从图片ID构建hiRes URL或从已知的hiRes URL列表中查找匹配的URL
        
        :param image_id: 图片ID，例如 "51ZxrcCKRgL"
        :param known_hires_urls: 可选，已知的hiRes URL列表
        :return: hiRes URL或None
        """
        if not image_id:
            return None
            
        # 1. 尝试从已知的hiRes URL列表中查找匹配的URL
        if known_hires_urls and isinstance(known_hires_urls, list):
            for url in known_hires_urls:
                if image_id in url and any(high_res_suffix in url for high_res_suffix in ["_SL1500_", "_SL1000_", "_SL2000_"]):
                    if self.verbose:
                        print(f"通过图片ID {image_id} 在已知列表中找到高清图: {url}")
                    return url
        
        # 2. 尝试构建多种可能的hiRes URL格式
        hires_variants = [
            f"https://m.media-amazon.com/images/I/{image_id}._AC_SL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._SL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._AC_UL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._AC_.jpg"
        ]
        
        # 返回最可能的高分辨率URL
        return hires_variants[0]
        
    def _build_image_variants(self, image_id):
        """
        构建图片ID的多种可能URL变体
        
        :param image_id: 图片ID，例如 "51ZxrcCKRgL"
        :return: 可能的URL列表
        """
        if not image_id:
            return []
        
        # 尝试不同的图片格式
        variants = [
            f"https://m.media-amazon.com/images/I/{image_id}._AC_SL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._SL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._AC_UL1500_.jpg",
            f"https://m.media-amazon.com/images/I/{image_id}._AC_.jpg"
        ]
        
        return variants

    def _debug_print_variants_images(self, variants, stage=""):
        """
        调试函数：打印变体图片提取的详细信息
        :param variants: 变体列表
        :param stage: 当前阶段名称
        """
        if not self.verbose:
            return
            
        print(f"\n{'='*20} 变体图片提取调试信息 - {stage} {'='*20}")
        print(f"共有 {len(variants)} 个变体")
        
        # 统计有图片的变体数量
        variants_with_images = [v for v in variants if v.get('image_url')]
        print(f"其中 {len(variants_with_images)} 个变体有图片")
        
        # 打印每个变体的信息
        for i, variant in enumerate(variants):
            asin = variant.get('asin', 'unknown')
            image_url = variant.get('image_url', 'no-image')
            
            # 获取颜色信息
            color_info = "无颜色信息"
            for dim_name, spec_info in variant.get('specs', {}).items():
                if any(color_term in dim_name.lower() for color_term in ['color', 'colour', 'farbe', '颜色']):
                    color_value = spec_info.get('value', '')
                    color_info = f"{dim_name}: {color_value}"
                    break
                    
            # 打印变体信息
            print(f"变体 {i+1}/{len(variants)} - ASIN: {asin}")
            print(f"  颜色信息: {color_info}")
            print(f"  图片URL: {image_url[:100]}..." if len(image_url) > 100 else f"  图片URL: {image_url}")
            print("-" * 50)
        
        print(f"{'='*20} 变体图片提取调试信息结束 - {stage} {'='*20}\n")

    def _map_color_name(self, color_name):
        """将颜色名称映射到标准化的颜色名称"""
        if not color_name:
            return ""
            
        # 颜色名称映射字典（扩展版）
        color_map = {
            # 德语
            'marineblau': 'navy',
            'rot': 'red',
            'schwarz': 'black',
            'weiss': 'white',
            'weiß': 'white',
            'blau': 'blue',
            'grün': 'green',
            'grun': 'green',
            'grau': 'grey',
            'gelb': 'yellow',
            'rosa': 'pink',
            'lila': 'purple',
            'violett': 'purple',
            'braun': 'brown',
            'orange': 'orange',
            'türkis': 'turquoise',
            'turkis': 'turquoise',
            
            # 法语
            'noir': 'black',
            'blanc': 'white',
            'bleu': 'blue',
            'rouge': 'red',
            'vert': 'green',
            'jaune': 'yellow',
            'rose': 'pink',
            'violet': 'purple',
            'marron': 'brown',
            'gris': 'grey',
            
            # 西班牙语
            'negro': 'black',
            'blanco': 'white',
            'azul': 'blue',
            'rojo': 'red',
            'verde': 'green',
            'amarillo': 'yellow',
            'rosado': 'pink',
            'morado': 'purple',
            'marrón': 'brown',
            'marron': 'brown',
            'gris': 'grey',
            
            # 意大利语
            'nero': 'black',
            'bianco': 'white',
            'blu': 'blue',
            'rosso': 'red',
            'verde': 'green',
            'giallo': 'yellow',
            'rosa': 'pink',
            'viola': 'purple',
            'marrone': 'brown',
            'grigio': 'grey',
            
            # 中文
            '黑色': 'black',
            '白色': 'white',
            '蓝色': 'blue',
            '红色': 'red',
            '绿色': 'green',
            '黄色': 'yellow',
            '粉色': 'pink',
            '紫色': 'purple',
            '棕色': 'brown',
            '灰色': 'grey',
            
            # 常见颜色变体
            'navy': 'navy',
            'navy blue': 'navy',
            'dark blue': 'navy',
            'marine': 'navy',
            'crimson': 'red',
            'scarlet': 'red',
            'burgundy': 'red',
            'wine': 'red',
            'charcoal': 'grey',
            'silver': 'grey',
            'ash': 'grey',
            'ivory': 'white',
            'cream': 'white',
            'beige': 'brown',
            'khaki': 'brown',
            'tan': 'brown',
            'olive': 'green',
            'lime': 'green',
            'teal': 'green',
            'aqua': 'blue',
            'cyan': 'blue',
            'indigo': 'blue',
            'magenta': 'pink',
            'fuchsia': 'pink',
            'coral': 'pink',
            'salmon': 'pink',
            'gold': 'yellow',
            'mustard': 'yellow',
            'lavender': 'purple',
            'plum': 'purple',
            'mauve': 'purple',
        }
        
        # 尝试直接匹配（忽略大小写）
        color_lower = color_name.lower()
        if color_lower in color_map:
            return color_map[color_lower]
            
        # 尝试部分匹配
        for key, value in color_map.items():
            if key in color_lower or color_lower in key:
                return value
                
        # 如果没有匹配，返回原始颜色名
        return color_lower
        
    def _find_best_color_match(self, color_name, available_colors, threshold=0.7):
        """
        查找最佳颜色匹配
        
        :param color_name: 要匹配的颜色名称
        :param available_colors: 可用的颜色名称列表
        :param threshold: 相似度阈值，超过此值才认为匹配
        :return: 最佳匹配的颜色名称，如果没有匹配则返回None
        """
        if not color_name or not available_colors:
            return None
            
        # 标准化颜色名称
        mapped_color = self._map_color_name(color_name)
        
        best_match = None
        best_score = 0
        
        for avail_color in available_colors:
            # 跳过ASIN键
            if len(avail_color) == 10 and re.match(r'^[A-Z0-9]+$', avail_color):
                continue
                
            # 计算相似度
            avail_mapped = self._map_color_name(avail_color)
            
            # 1. 直接匹配标准化后的颜色名
            if mapped_color == avail_mapped:
                return avail_color
                
            # 2. 计算字符串相似度
            score = self._calculate_string_similarity(mapped_color, avail_mapped)
            
            if score > best_score and score >= threshold:
                best_score = score
                best_match = avail_color
                
        return best_match
        
    def _calculate_string_similarity(self, s1, s2):
        """计算两个字符串的相似度（0-1之间）"""
        if not s1 or not s2:
            return 0
            
        # 转换为小写
        s1, s2 = s1.lower(), s2.lower()
        
        # 如果一个是另一个的子字符串，给予高分
        if s1 in s2:
            return 0.9
        if s2 in s1:
            return 0.9
            
        # 计算编辑距离
        len_s1, len_s2 = len(s1), len(s2)
        if len_s1 == 0 or len_s2 == 0:
            return 0
            
        # 初始化矩阵
        matrix = [[0 for _ in range(len_s2 + 1)] for _ in range(len_s1 + 1)]
        
        # 填充第一行和第一列
        for i in range(len_s1 + 1):
            matrix[i][0] = i
        for j in range(len_s2 + 1):
            matrix[0][j] = j
            
        # 填充矩阵
        for i in range(1, len_s1 + 1):
            for j in range(1, len_s2 + 1):
                cost = 0 if s1[i-1] == s2[j-1] else 1
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # 删除
                    matrix[i][j-1] + 1,      # 插入
                    matrix[i-1][j-1] + cost  # 替换
                )
                
        # 计算相似度
        distance = matrix[len_s1][len_s2]
        max_len = max(len_s1, len_s2)
        similarity = 1 - (distance / max_len)
        
        return similarity

    def _debug_print_variants_images(self, variants, stage=""):
        """
        调试函数：打印变体图片提取的详细信息
        :param variants: 变体列表
        :param stage: 当前阶段名称
        """
        if not self.verbose:
            return
            
        print(f"\n{'='*20} 变体图片提取调试信息 - {stage} {'='*20}")
        print(f"共有 {len(variants)} 个变体")
        
        # 统计有图片的变体数量
        variants_with_images = [v for v in variants if v.get('image_url')]
        print(f"其中 {len(variants_with_images)} 个变体有图片")
        
        # 打印每个变体的信息
        for i, variant in enumerate(variants):
            asin = variant.get('asin', 'unknown')
            image_url = variant.get('image_url', 'no-image')
            
            # 获取颜色信息
            color_info = "无颜色信息"
            for dim_name, spec_info in variant.get('specs', {}).items():
                if any(color_term in dim_name.lower() for color_term in ['color', 'colour', 'farbe', '颜色']):
                    color_value = spec_info.get('value', '')
                    color_info = f"{dim_name}: {color_value}"
                    break
                    
            # 打印变体信息
            print(f"变体 {i+1}/{len(variants)} - ASIN: {asin}")
            print(f"  颜色信息: {color_info}")
            print(f"  图片URL: {image_url[:100]}..." if len(image_url) > 100 else f"  图片URL: {image_url}")
            print("-" * 50)
        
        print(f"{'='*20} 变体图片提取调试信息结束 - {stage} {'='*20}\n")

    def _fetch_single_price(self, asin, parent_asin):
        """从 miniDetail Ajax 获取单个 ASIN 的价格，失败返回 None"""
        try:
            if not asin or not parent_asin or not self.session:
                return None
            # 构造 URL（使用动态检测的域名）
            domain = self.amazon_domain or "amazon.de"
            ajax_url = (
                f"https://www.{domain}/gp/twister/miniDetail?asin={asin}&"
                f"parentAsin={parent_asin}&mType=VARIATION&smid=ATVPDKIKX0DER"
            )
            r = self.session.get(ajax_url, headers=self.headers, proxies=self.proxy, timeout=15)
            if r.status_code != 200:
                return None
            # 查找 price / formattedPrice
            m = re.search(r'"formattedPrice"\s*:\s*"([^"]+)"', r.text)
            if not m:
                m = re.search(r'"price"\s*:\s*"([^"]+)"', r.text)
            if m:
                # 去掉货币符号，仅保留数字和小数点
                price_clean = re.sub(r'[€,£,$¥₹ ]', '', m.group(1))
                return price_clean
            return None
        except Exception:
            return None

    def _enrich_variants_with_price_stock(self, variants, twister_data):
        """
        使用twister数据丰富变体的价格和库存信息，加入重试逻辑；
        先批量请求，失败部分再单 ASIN 请求。
        """
        if not variants:
            return [] if variants is None else variants
        try:
            default_price = self.product_data.get('价格', '')
            currency_symbol = self.product_data.get('货币符号', '')

            asin_list = []
            asin_to_variant = {}
            parent_asin = twister_data.get('parentAsin') or twister_data.get('currentAsin')
            if not parent_asin and variants:
                parent_asin = variants[0].get('asin')
            for v in variants:
                asin = v.get('asin')
                if asin:
                    asin_list.append(asin)
                    asin_to_variant[asin] = v

            price_map = {}
            # -------- 1) 批量 Ajax，最多 3 次 --------
            max_bulk_retry = 3
            for _ in range(max_bulk_retry):
                missing = [a for a in asin_list if a not in price_map]
                if not missing:
                    break
                bulk_res = self._fetch_variants_price_bulk(missing, parent_asin)
                price_map.update(bulk_res)
                # 轻微退避
                time.sleep(0.8)

            # -------- 2) 单 ASIN Ajax 兜底 --------
            still_missing = [a for a in asin_list if a not in price_map]
            for asin in still_missing:
                p = self._fetch_single_price(asin, parent_asin)
                if p:
                    price_map[asin] = p
                    if self.verbose:
                        print(f"  单 ASIN 获取到价格 {p} -> {asin}")

            # -------- 3) dimensionValuesData 兜底 --------
            dim_data = twister_data.get('dimensionValuesData', {})
            for asin in asin_list:
                if asin not in price_map and asin in dim_data:
                    price_val = dim_data[asin].get('price') or dim_data[asin].get('priceFormatted', '')
                    if price_val:
                        price_clean = re.sub(r'[€,£,$¥₹ ]', '', str(price_val))
                        price_map[asin] = price_clean

            # -------- 4) 应用价格映射 --------
            for v in variants:
                asin = v.get('asin')
                price_val = price_map.get(asin)
                if price_val:
                    v['sale_price'] = price_val
                    v['sale_price_formatted'] = f"{price_val}"
                else:
                    v['sale_price'] = default_price or ''
                    v['sale_price_formatted'] = f"{default_price}" if default_price else ''
                v['stock_quantity'] = 999

            # -------- 5) 回填最低价 --------
            if price_map:
                fallback = min(float(p) for p in price_map.values() if p)
                for v in variants:
                    if not v.get('sale_price'):
                        v['sale_price'] = str(fallback)
                        v['sale_price_formatted'] = str(fallback)
                        if self.verbose:
                            print(f"  回填价格 {fallback} -> {v.get('asin')}")

            if self.verbose:
                got = len(price_map)
                print(f"变体价格获取完成，成功 {got}/{len(asin_list)} 个 ASIN")
            return variants
        except Exception as e:
            if self.verbose:
                print(f"丰富变体价格信息出错: {e}")
            return variants

def get_random_user_agent():
    """获取随机User-Agent"""
    user_agents = [
        # Windows浏览器
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 Edge/16.16299",
        "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:54.0) Gecko/20100101 Firefox/54.0",
        "Mozilla/5.0 (Windows NT 10.0; Win32) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.62",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.70",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36 OPR/77.0.4054.277",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36 Edg/91.0.864.71",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.115 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Windows NT 10.0; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.67",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.203",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36 OPR/77.0.4054.275",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.73",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 OPR/78.0.4093.147",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36 Edge/14.14393",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 Edge/16.16299",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36 Edge/14.14393",
        "Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; rv:11.0) like Gecko",
        "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36 Edge/14.14393",
        "Mozilla/5.0 (Windows NT 6.2; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
        "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 Edge/16.16299",
        "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.3; Trident/7.0; rv:11.0) like Gecko",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36 Edge/14.14393",
        "Mozilla/5.0 (Windows NT 5.1; rv:52.0) Gecko/20100101 Firefox/52.0",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.79 Safari/537.36",
        
        # Mac浏览器
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.3 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15",
        
        # Linux浏览器
        "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (X11; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64; rv:78.0) Gecko/20100101 Firefox/78.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Fedora; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
        "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0",
        
        # 移动设备浏览器
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/92.0.4515.90 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Mobile Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/120.0.6099.101 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/119.0.6045.109 Mobile/15E148 Safari/604.1"
    ]
    return random.choice(user_agents)

def verify_proxy(proxy, timeout=5, test_url="https://www.amazon.de"):
    """
    验证代理是否可用
    :param proxy: 代理字典 {'http': '*******************:port/', 'https': '*******************:port/'}
    :param timeout: 超时时间（秒）
    :param test_url: 测试URL
    :return: 是否可用
    """
    try:
        print(f"[代理验证] 正在验证代理: {proxy['http']}")
        start_time = time.time()
        
        # 先测试基本连接
        basic_test = requests.get(
            'https://ipv4.webshare.io/',
            proxies=proxy,
            timeout=timeout
        )
        
        if basic_test.status_code != 200:
            print(f"[代理验证] 基本连接测试失败: 状态码 {basic_test.status_code}")
            return False
            
        # 再测试目标网站
        response = requests.get(
            test_url,
            proxies=proxy,
            headers=get_headers_with_currency(),
            timeout=timeout
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"[代理验证] 代理验证成功! 响应时间: {response_time:.2f}秒")
            return True
        else:
            print(f"[代理验证] 代理验证失败: 状态码 {response.status_code}")
            return False
    except Exception as e:
        print(f"[代理验证] 代理验证异常: {str(e)}")
        return False

def get_random_proxy(proxy_file="proxies.txt", proxy_list_url=None, failed_proxies=None, verify=True):
    """
    获取随机代理，优先从本地文件读取，如果本地文件不存在或为空则尝试从URL获取
    增加了代理验证功能，确保返回可用的代理
    """
    if failed_proxies is None:
        failed_proxies = set()
    
    # 优先从本地文件读取代理
    if os.path.exists(proxy_file) and os.path.getsize(proxy_file) > 0:
        try:
            with open(proxy_file, 'r') as f:
                proxies = []
                for line in f.readlines():
                    line = line.strip()
                    if line:
                        parts = line.split(':')
                        if len(parts) == 4:  # 格式: IP:PORT:USERNAME:PASSWORD
                            ip, port, username, password = parts
                            proxy = {
                                "http": f"http://{username}:{password}@{ip}:{port}/",
                                "https": f"http://{username}:{password}@{ip}:{port}/"
                            }
                            # 如果代理不在失败列表中，则添加到候选列表
                            proxy_key = f"{ip}:{port}"
                            if proxy_key not in failed_proxies:
                                proxies.append(proxy)
                
                if proxies:
                    # 随机选择一个代理
                    proxy = random.choice(proxies)
                    
                    # 如果需要验证，则验证代理
                    if verify:
                        print(f"[代理验证] 正在验证代理: {proxy['http']}")
                        if verify_proxy(proxy):
                            print(f"[代理验证] 代理验证成功: {proxy['http']}")
                            return proxy
                        else:
                            print(f"[代理验证] 代理验证失败: {proxy['http']}")
                            # 将失败的代理添加到失败列表
                            ip_port = proxy['http'].split('@')[1].split('/')[0]
                            failed_proxies.add(ip_port)
                            # 递归调用自身获取另一个代理
                            return get_random_proxy(proxy_file, proxy_list_url, failed_proxies, verify)
                    else:
                        return proxy
        except Exception as e:
            print(f"[代理错误] 从文件读取代理时出错: {str(e)}")
    
    # 如果本地文件不存在或为空，尝试从URL获取
    if proxy_list_url:
        try:
            response = requests.get(proxy_list_url, timeout=10)
            if response.status_code == 200:
                # 保存到本地文件
                with open(proxy_file, 'w') as f:
                    f.write(response.text)
                # 递归调用自身从新保存的文件中获取代理
                return get_random_proxy(proxy_file, None, failed_proxies, verify)
        except Exception as e:
            print(f"[代理错误] 从URL获取代理时出错: {str(e)}")
    
    # 如果所有代理都失败或没有可用代理，返回None
    print("[代理错误] 没有可用代理")
    return None

def get_headers_with_currency(currency='EUR'):
    """生成带有货币类型Cookie的请求头，优化为更像真实浏览器访问"""
    # 使用随机UA
    user_agent = get_random_user_agent()
    
    # 构造更真实的Cookie和会话标识
    session_id = f"{random.randint(100000000, 999999999)}-{random.randint(1000000000, 9999999999)}"
    # 生成随机token
    session_token = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', k=30))
    ubid = ''.join(random.choices('0123456789ABCDEF', k=24))
    
    headers = {
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': user_agent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-User': '?1',
        'Sec-Fetch-Dest': 'document',
        'Accept-Language': 'de_DE,de;q=0.9,de;q=0.8',
        'Cookie': f'i18n-prefs={currency};lc-acbde=de_DE;session-id={session_id};session-token={session_token};ubid-acbde={ubid}',
        'Referer': 'https://www.amazon.de/',
        'sec-ch-ua': '"Google Chrome";v="120", "Chromium";v="120", "Not?A_Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Cache-Control': 'max-age=0'
    }
    return headers

def fetch_proxy_list(proxy_list_url, output_file="proxies.txt"):
    """
    从指定URL获取代理列表并保存到本地文件
    """
    try:
        print(f"正在从 {proxy_list_url} 获取代理列表...")
        response = requests.get(proxy_list_url)
        if response.status_code == 200:
            with open(output_file, 'w') as f:
                f.write(response.text)
            
            # 统计代理数量
            proxy_count = len(response.text.strip().split('\n'))
            print(f"✅ 成功获取并保存 {proxy_count} 个代理到 {output_file}")
            return True
        else:
            print(f"❌ 获取代理列表失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取代理列表时出错: {e}")
        return False

def fetch_amazon_product(url, currency="EUR", proxy_list_url=None, api_url="http://**************/wms/product/importProducts", warehouse_id="3079", html_file=None, verbose=True, save_file=True, send_api=True):
    """
    获取亚马逊产品信息，直接处理HTML或从URL获取
    
    :param url: 亚马逊产品URL
    :param currency: 货币代码 (EUR, USD等)
    :param proxy_list_url: 代理列表URL
    :param api_url: API接口URL
    :param warehouse_id: 仓库ID
    :param html_file: HTML文件路径 (如果提供，直接使用文件内容)
    :param verbose: 是否显示详细信息
    :param save_file: 是否保存到文件
    :param send_api: 是否发送到API
    :return: 产品信息字典
    """
    try:
        start_time = time.time()
        
        # 设置输出文件名
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        output_file = f"amazon_product_{url_hash}.txt"
        
        # 如果提供了HTML文件，直接使用文件内容
        html_content = None
        if html_file:
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                if verbose:
                    print(f"从文件 {html_file} 加载HTML内容，长度: {len(html_content)} 字节")
            except Exception as e:
                print(f"读取HTML文件时出错: {e}")
                return None
        
        # 如果没有HTML内容，则从URL获取
        session = None
        headers = None
        proxy = None
        
        if not html_content:
            # 获取代理
            proxy_dict = None
            try:
                proxy_url = get_random_proxy(proxy_list_url=proxy_list_url, verify=True)
                if proxy_url:
                    proxy_dict = {'http': proxy_url, 'https': proxy_url}
                    if verbose:
                        print(f"使用代理: {proxy_url}")
            except Exception as e:
                if verbose:
                    print(f"获取代理时出错: {e}")
            
            # 准备请求头
            headers = get_headers_with_currency(currency)
            
            # 创建会话
            session = requests.Session()
            
            # 发送请求
            try:
                if verbose:
                    print(f"发送请求到 {url}...")
                
                response = session.get(
                    url, 
                    headers=headers, 
                    proxies=proxy_dict,
                    timeout=30,
                    allow_redirects=True
                )
                
                if response.status_code != 200:
                    if verbose:
                        print(f"请求失败，状态码: {response.status_code}")
                    return None
                
                html_content = response.text
                
                if verbose:
                    print(f"请求状态码: {response.status_code}")
                    print(f"HTML内容长度: {len(html_content)} 字节")
                
                # 检查是否是有效的亚马逊产品页面
                if "Amazon CAPTCHA" in html_content or "Bot Check" in html_content:
                    if verbose:
                        print("遇到亚马逊验证码页面，请更换代理或等待一段时间")
                    return None
                
                if len(html_content) < 20000:  # HTML内容太短可能是错误页面
                    if verbose:
                        print(f"HTML内容长度过短({len(html_content)})，可能是受限页面")
                    return None
                
                proxy = proxy_dict
                
            except Exception as e:
                if verbose:
                    print(f"请求URL时出错: {e}")
                return None
        
        # 创建亚马逊产品信息提取器
        scraper = AmazonProductScraper(
            html_content=html_content,
            verbose=verbose,
            session=session,
            headers=headers,
            proxy=proxy
        )
        
        if scraper.load_html():
            if not scraper.extract_product_info():
                print("⚠️ 从本地HTML文件提取产品信息失败")
                return {"success": False, "status_code": None, "error": "提取产品信息失败", "html_content": None}
            
            # 先保存到文件
            file_saved = False
            if save_file:
                file_saved = scraper.save_to_file("amazon.txt")
                if file_saved and verbose:
                    print(f"产品信息已保存到 amazon.txt")
            
            # 如果需要，发送到API
            api_result = {"success": False, "message": "未发送到API"}
            if send_api:
                api_result = scraper.send_to_api(api_url=api_url, warehouse_id=warehouse_id, source_link=url)
                if verbose:
                    print(f"API发布结果: {'成功' if api_result.get('success', False) else '失败'}")
                    if 'error' in api_result:
                        print(f"错误信息: {api_result['error']}")
            
            result = {
                "success": True,
                "product_data": scraper.product_data,
                "file_saved": file_saved,
                "api_result": api_result
            }
            return result
        else:
            print("本地HTML解析失败")
            return {"success": False, "status_code": None, "error": "本地HTML解析失败", "html_content": None}
    except Exception as e:
        print(f"❌ 采集产品信息时出错: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "status_code": None, "error": str(e), "html_content": None}

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='亚马逊产品信息采集工具')
    parser.add_argument('--url', type=str, help='亚马逊产品URL')
    parser.add_argument('--file', type=str, help='包含多个URL的文件，每行一个')
    parser.add_argument('--html', type=str, help='本地HTML文件路径')
    parser.add_argument('--currency', type=str, default='EUR', help='货币类型 (EUR, USD等)')
    parser.add_argument('--proxy', type=str, help='代理列表URL')
    parser.add_argument('--fetch-proxy', action='store_true', help='从代理URL获取代理列表并保存')
    parser.add_argument('--proxy-file', type=str, default='proxies.txt', help='保存代理的文件名')
    parser.add_argument('--api', type=str, default='http://**************/wms/product/importProducts', help='API接口URL')
    parser.add_argument('--warehouse', type=str, default='3079', help='仓库ID')
    parser.add_argument('--debug', action='store_true', help='启用调试输出')
    parser.add_argument('--no-save', action='store_true', help='不保存到文件')
    parser.add_argument('--no-api', action='store_true', help='不发送到API')
    parser.add_argument('--output', type=str, default='amazon.txt', help='输出文件名')
    
    args = parser.parse_args()
    
    # 设置详细输出
    verbose = True
    if args.debug:
        print("启用调试模式 - 将输出详细日志")
        verbose = True
    
    # 如果指定了--fetch-proxy，则获取代理列表
    if args.fetch_proxy and args.proxy:
        fetch_proxy_list(args.proxy, args.proxy_file)
        if not args.url and not args.file and not args.html:
            return  # 如果只是获取代理，不执行其他操作
    
    # 处理本地HTML文件
    if args.html:
        print(f"使用本地HTML文件: {args.html}")
        result = fetch_amazon_product(
            url="",  # URL为空，因为使用本地文件
            currency=args.currency,
            proxy_list_url=args.proxy,
            api_url=args.api,
            warehouse_id=args.warehouse,
            html_file=args.html,
            verbose=verbose,
            save_file=not args.no_save,
            send_api=not args.no_api
        )
        
        # 输出API发布结果
        if not args.no_api and result and "api_result" in result:
            print("\n===== API发布状态 =====")
            api_result = result["api_result"]
            if api_result.get("success", False):
                print(f"✅ API发布成功!")
            else:
                print(f"❌ API发布失败: {api_result.get('error', '未知错误')}")
            print("=====================\n")
        
        # 如果成功提取变体，输出变体组合格式
        if result and "product_data" in result and result["product_data"].get('产品变体'):
            print("\n====== 变体组合格式 ======")
            scraper = AmazonProductScraper("", verbose=False)
            comb_format = scraper._generate_combinations_format(result["product_data"]['产品变体'])
            if comb_format:
                print("规格组:")
                for group_id, group_info in comb_format.get('spec_groups', {}).items():
                    print(f"  组 {group_id}: {group_info['name']}")
                    print(f"    是否颜色: {'是' if group_info.get('is_color') else '否'}")
                    print(f"    值: {group_info.get('values', {})}")
                
                print("\n组合:")
                for i, comb in enumerate(comb_format.get('combination', []), 1):
                    print(f"  组合 {i}: {comb.get('skuattr')}")
                    print(f"    价格: {comb.get('price')}")
                    print(f"    库存: {comb.get('qty')}")
                    print(f"    图片: {(comb.get('image_url') or '')[:50]}...")
            else:
                print("无法生成组合格式")
            print("========================\n")
        
        return
    
    # 处理单个URL
    if args.url:
        print(f"开始采集单个产品: {args.url}")
        result = fetch_amazon_product(
            url=args.url,
            currency=args.currency,
            proxy_list_url=args.proxy,
            api_url=args.api,
            warehouse_id=args.warehouse,
            verbose=verbose,
            save_file=not args.no_save,
            send_api=not args.no_api
        )
        
        # 输出API发布结果
        if not args.no_api and result and "api_result" in result:
            print("\n===== API发布状态 =====")
            api_result = result["api_result"]
            if api_result.get("success", False):
                print(f"✅ API发布成功!")
            else:
                print(f"❌ API发布失败: {api_result.get('error', '未知错误')}")
            print("=====================\n")
        return
    
    # 处理文件中的多个URL
    if args.file:
        try:
            with open(args.file, 'r') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            print(f"从文件加载了 {len(urls)} 个URL")
            
            results = []
            for i, url in enumerate(urls):
                print(f"\n[{i+1}/{len(urls)}] 开始采集: {url}")
                result = fetch_amazon_product(
                    url=url,
                    currency=args.currency,
                    proxy_list_url=args.proxy,
                    api_url=args.api,
                    warehouse_id=args.warehouse,
                    verbose=verbose,
                    save_file=not args.no_save,
                    send_api=not args.no_api
                )
                results.append({"url": url, "result": result})
                
                # 输出API发布结果
                if not args.no_api and result and "api_result" in result:
                    print("\n===== API发布状态 =====")
                    api_result = result["api_result"]
                    if api_result.get("success", False):
                        print(f"✅ API发布成功!")
                    else:
                        print(f"❌ API发布失败: {api_result.get('error', '未知错误')}")
                    print("=====================\n")
                
                # 随机延迟，避免请求过快
                if i < len(urls) - 1:
                    delay = random.uniform(5, 10)
                    print(f"等待 {delay:.2f} 秒后继续...")
                    time.sleep(delay)
            
            # 输出总结报告
            print("\n====== 采集任务总结 ======")
            success_count = sum(1 for item in results if item["result"] and item["result"].get("success", False))
            api_success_count = sum(1 for item in results if item["result"] and "api_result" in item["result"] and item["result"]["api_result"].get("success", False))
            print(f"总URL数: {len(urls)}")
            print(f"成功采集数: {success_count}")
            print(f"API发布成功数: {api_success_count}")
            print("=========================\n")
            return
                
        except Exception as e:
            print(f"处理文件时出错: {e}")
    
    # 没有提供URL、文件或HTML，使用默认本地HTML文件
    html_file = "网页代码.html"
    if os.path.exists(html_file):
        print(f"未提供参数，使用本地HTML文件: {html_file}")
        result = fetch_amazon_product(
            url="",
            currency=args.currency,
            proxy_list_url=args.proxy,
            api_url=args.api,
            warehouse_id=args.warehouse,
            html_file=html_file,
            verbose=verbose,
            save_file=not args.no_save,
            send_api=not args.no_api
        )
        
        # 输出API发布结果
        if not args.no_api and result and "api_result" in result:
            print("\n===== API发布状态 =====")
            api_result = result["api_result"]
            if api_result.get("success", False):
                print(f"✅ API发布成功!")
            else:
                print(f"❌ API发布失败: {api_result.get('error', '未知错误')}")
            print("=====================\n")
    else:
        # 示例URL
        example_url = "https://www.amazon.de/dp/B0DQPHTC18/"
        print(f"本地HTML文件 {html_file} 不存在，使用示例URL进行测试")
        result = fetch_amazon_product(
            url=example_url,
            currency=args.currency,
            proxy_list_url=args.proxy,
            api_url=args.api,
            warehouse_id=args.warehouse,
            verbose=verbose,
            save_file=not args.no_save,
            send_api=not args.no_api
        )
        
        # 输出API发布结果
        if not args.no_api and result and "api_result" in result:
            print("\n===== API发布状态 =====")
            api_result = result["api_result"]
            if api_result.get("success", False):
                print(f"✅ API发布成功!")
            else:
                print(f"❌ API发布失败: {api_result.get('error', '未知错误')}")
            print("=====================\n")

if __name__ == "__main__":
    main()