<?php
/**
 * 接口: GET /crawl/getNextProduct.php
 * 功能: 领取一条待处理的产品数据 (status=0)，并将 status 更新为 1
 * 参数(可选):
 *   warehouse_id  指定仓库，仅返回对应仓库数据
 *   min_sales     仅领取销售量大于该值的产品（可选，整数）；兼容别名 sales
 *   delete_id     根据ID删除该产品（可选，整数；仅当提供时执行删除并返回结果）
 * 返回:
 *   code 0   有数据
 *   code 1   暂无待处理数据
 *   其他      错误
 */

declare(strict_types=1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => -1, 'msg' => 'Method Not Allowed'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

require_once __DIR__ . '/db.php';
$pdo = get_pdo();

$warehouseId = isset($_GET['warehouse_id']) ? (int)$_GET['warehouse_id'] : 0;
$minSales   = isset($_GET['min_sales']) ? (int)$_GET['min_sales'] : (isset($_GET['sales']) ? (int)$_GET['sales'] : null);
$deleteId   = isset($_GET['delete_id']) ? (int)$_GET['delete_id'] : 0;

// 删除分支：当传入 delete_id 时，按ID删除并返回
if ($deleteId > 0) {
    try {
        $stmt = $pdo->prepare('DELETE FROM amazon_products WHERE id = :id');
        $stmt->execute([':id' => $deleteId]);
        $affected = (int)$stmt->rowCount();
        echo json_encode([
            'code' => 0,
            'msg' => 'deleted',
            'data' => [
                'id' => $deleteId,
                'affected_rows' => $affected
            ]
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } catch (Throwable $e) {
        http_response_code(500);
        echo json_encode([
            'code' => -1,
            'msg' => 'server error',
            'error' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    exit;
}

try {
    $pdo->beginTransaction();

    $conditions = ['status = 0'];
    $params     = [];
    if ($warehouseId > 0) {
        $conditions[] = 'warehouse_id = :wid';
        $params[':wid'] = $warehouseId;
    }
    if ($minSales !== null) {
        $conditions[] = 'sales > :min_sales';
        $params[':min_sales'] = $minSales;
    }

    $sql = sprintf(
        'SELECT id, product_url, price, sales, warehouse_id 
         FROM amazon_products 
         WHERE %s 
         ORDER BY id ASC 
         LIMIT 1 FOR UPDATE',
        implode(' AND ', $conditions)
    );
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $row = $stmt->fetch();

    if (!$row) {
        $pdo->commit();
        echo json_encode(['code' => 1, 'msg' => 'no data'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }

    $update = $pdo->prepare('UPDATE amazon_products SET status = 1 WHERE id = :id');
    $update->execute([':id' => $row['id']]);

    $pdo->commit();

    // 补全 https://www. 前缀
    $productUrl = $row['product_url'];
    if (stripos($productUrl, 'http') !== 0) {
        $productUrl = 'https://www.' . ltrim($productUrl, '/');
    } else {
        $parsed = parse_url($productUrl);
        if (!empty($parsed['host']) && stripos($parsed['host'], 'www.') !== 0) {
            $productUrl = $parsed['scheme'] . '://www.' . $parsed['host'] . (isset($parsed['path']) ? $parsed['path'] : '');
        }
    }

    $result = [
        'id'          => (int)$row['id'],
        'product_url' => $productUrl,
        'price'       => $row['price'] !== null ? (float)$row['price'] : null,
        'sales'       => (int)$row['sales'],
        'warehouse_id'=> isset($row['warehouse_id']) ? (int)$row['warehouse_id'] : null,
    ];

    echo json_encode(
        ['code' => 0, 'msg' => 'success', 'data' => $result],
        JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
    );
} catch (Throwable $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(
        ['code' => -1, 'msg' => 'server error', 'error' => $e->getMessage()],
        JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
    );
}
