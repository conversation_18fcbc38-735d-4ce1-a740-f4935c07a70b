#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Amazon域名检测功能使用示例
演示如何根据HTML内容自动检测Amazon域名并构建正确的变体价格URL
"""

from amazon_scraper import AmazonProductScraper

def demonstrate_domain_detection():
    """演示域名检测功能"""
    
    print("Amazon域名自动检测功能演示")
    print("=" * 50)
    
    # 模拟不同Amazon站点的HTML内容
    examples = [
        {
            "site": "Amazon德国站",
            "html": '''
            <html lang="de-DE">
            <head>
                <link rel="canonical" href="https://www.amazon.de/dp/B0DQPHTC18/">
                <title>Amazon.de: Produkt</title>
            </head>
            <body>
                <div id="productTitle">Test Product</div>
                <a href="https://www.amazon.de/gp/product/B0DQPHTC18">产品链接</a>
                <script>
                    var twisterData = {
                        "parentAsin": "B0DQPHTC18",
                        "currentAsin": "B0DQPHTC19"
                    };
                </script>
            </body>
            </html>
            '''
        },
        {
            "site": "Amazon美国站",
            "html": '''
            <html lang="en-US">
            <head>
                <link rel="canonical" href="https://www.amazon.com/dp/B0DQPHTC18/">
                <title>Amazon.com: Product</title>
            </head>
            <body>
                <div id="productTitle">Test Product</div>
                <a href="https://www.amazon.com/gp/product/B0DQPHTC18">Product link</a>
                <script>
                    var twisterData = {
                        "parentAsin": "B0DQPHTC18",
                        "currentAsin": "B0DQPHTC19"
                    };
                </script>
            </body>
            </html>
            '''
        },
        {
            "site": "Amazon英国站",
            "html": '''
            <html lang="en-GB">
            <head>
                <link rel="canonical" href="https://www.amazon.co.uk/dp/B0DQPHTC18/">
                <title>Amazon.co.uk: Product</title>
            </head>
            <body>
                <div id="productTitle">Test Product</div>
                <a href="https://www.amazon.co.uk/gp/product/B0DQPHTC18">Product link</a>
            </body>
            </html>
            '''
        }
    ]
    
    for example in examples:
        print(f"\n🌐 {example['site']}")
        print("-" * 30)
        
        # 创建scraper实例
        scraper = AmazonProductScraper(html_content=example['html'], verbose=False)
        
        # 加载HTML并检测域名
        if scraper.load_html():
            detected_domain = scraper._detect_amazon_domain()
            scraper.amazon_domain = detected_domain
            
            print(f"✅ 检测到域名: {detected_domain}")
            
            # 展示变体价格URL构建
            variant_price_url = f"https://www.{detected_domain}/gp/product/ajax"
            print(f"📡 变体价格URL: {variant_price_url}")
            
            # 展示单个价格URL构建
            single_price_url = f"https://www.{detected_domain}/gp/twister/miniDetail"
            print(f"🔍 单个价格URL: {single_price_url}")
            
            # 展示Referer设置
            referer = f"https://www.{detected_domain}/"
            print(f"🔗 Referer设置: {referer}")
        else:
            print("❌ HTML加载失败")

def show_manual_domain_setting():
    """演示手动设置域名功能"""
    
    print("\n\n手动设置域名功能演示")
    print("=" * 50)
    
    # 创建scraper实例
    scraper = AmazonProductScraper(verbose=False)
    
    # 测试手动设置不同域名
    test_domains = ["amazon.de", "amazon.com", "amazon.co.uk", "amazon.fr", "invalid.domain"]
    
    for domain in test_domains:
        print(f"\n尝试设置域名: {domain}")
        scraper.set_amazon_domain(domain)
        
        if scraper.amazon_domain == domain:
            print(f"✅ 成功设置域名: {scraper.amazon_domain}")
            variant_url = f"https://www.{scraper.amazon_domain}/gp/product/ajax"
            print(f"📡 对应的变体价格URL: {variant_url}")
        else:
            print(f"❌ 域名设置失败")

def show_detection_methods():
    """展示不同的域名检测方法"""
    
    print("\n\n域名检测方法演示")
    print("=" * 50)
    
    detection_examples = [
        {
            "method": "Canonical URL检测",
            "html": '''
            <html>
            <head>
                <link rel="canonical" href="https://www.amazon.fr/dp/B0DQPHTC18/">
            </head>
            <body><p>Test</p></body>
            </html>
            '''
        },
        {
            "method": "页面链接检测",
            "html": '''
            <html>
            <body>
                <a href="https://www.amazon.it/gp/product/B0DQPHTC18">Link 1</a>
                <a href="https://www.amazon.it/dp/B0DQPHTC19">Link 2</a>
                <a href="https://www.amazon.it/gp/cart">Link 3</a>
            </body>
            </html>
            '''
        },
        {
            "method": "HTML内容统计检测",
            "html": '''
            <html>
            <body>
                <script>
                    var urls = [
                        "https://www.amazon.es/image1.jpg",
                        "https://www.amazon.es/image2.jpg",
                        "https://www.amazon.es/api/data"
                    ];
                </script>
            </body>
            </html>
            '''
        },
        {
            "method": "语言推断检测",
            "html": '''
            <html lang="it-IT">
            <body>
                <p>Nessun link Amazon qui</p>
            </body>
            </html>
            '''
        }
    ]
    
    for example in detection_examples:
        print(f"\n🔍 {example['method']}")
        print("-" * 25)
        
        scraper = AmazonProductScraper(html_content=example['html'], verbose=True)
        if scraper.load_html():
            detected_domain = scraper._detect_amazon_domain()
            print(f"检测结果: {detected_domain}")

if __name__ == "__main__":
    demonstrate_domain_detection()
    show_manual_domain_setting()
    show_detection_methods()
    
    print("\n\n" + "=" * 50)
    print("✨ 功能总结:")
    print("1. 自动从HTML中检测Amazon域名")
    print("2. 支持多种检测方法：canonical URL、页面链接、内容统计、语言推断")
    print("3. 动态构建正确的变体价格URL")
    print("4. 支持手动设置域名")
    print("5. 自动设置正确的Referer头")
    print("=" * 50)
