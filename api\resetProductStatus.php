<?php
declare(strict_types=1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

require_once __DIR__ . '/db.php';
$pdo = get_pdo();

function json_response(int $code, string $msg, $data = null): void {
	$res = ['code' => $code, 'msg' => $msg];
	if ($data !== null) {
		$res['data'] = $data;
	}
	echo json_encode($res, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	exit;
}

function log_reset_error(string $phase, $context): void {
	$line = sprintf('[%s] %s %s%s', date('Y-m-d H:i:s'), $phase, json_encode($context, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), PHP_EOL);
	error_log($line, 3, __DIR__ . '/resetProductStatus_error.log');
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
	http_response_code(405);
	json_response(-1, 'Method Not Allowed');
}

$warehouseId = isset($_GET['warehouse_id']) ? (int)$_GET['warehouse_id'] : 0;
$fromStatusRaw  = $_GET['from_status'] ?? 3;
$toStatusRaw    = $_GET['to_status'] ?? 0;

if ($warehouseId <= 0) {
	http_response_code(400);
	json_response(-1, 'warehouse_id参数必填且必须为正整数');
}

try {
	$pdo->beginTransaction();

	// 检测 status 列类型（数值型或字符串/ENUM），优先使用 SHOW COLUMNS，失败则默认按字符串处理
	$isStringStatus = true; // 默认按字符串/ENUM 处理，更贴合 batchSave 的写入
	$colTypeRaw = null;
	try {
		$colInfo = $pdo->query("SHOW COLUMNS FROM amazon_products LIKE 'status'")->fetch();
		if ($colInfo && isset($colInfo['Type'])) {
			$colTypeRaw = strtolower((string)$colInfo['Type']);
			if (strpos($colTypeRaw, 'int') !== false) {
				$isStringStatus = false;
			} else if (strpos($colTypeRaw, 'enum') !== false || strpos($colTypeRaw, 'char') !== false || strpos($colTypeRaw, 'text') !== false || strpos($colTypeRaw, 'set') !== false) {
				$isStringStatus = true;
			}
		}
	} catch (Throwable $detEx) {
		// 保持默认 isStringStatus=true，不中断流程
	}

	$map = [0 => 'extracted', 1 => 'processing', 2 => 'success', 3 => 'failed'];
	$fromStatus = $fromStatusRaw;
	$toStatus = $toStatusRaw;
	if ($isStringStatus) {
		if (is_numeric($fromStatusRaw)) {
			$fromStatus = $map[(int)$fromStatusRaw] ?? (string)$fromStatusRaw;
		} else {
			$fromStatus = (string)$fromStatusRaw;
		}
		if (is_numeric($toStatusRaw)) {
			$toStatus = $map[(int)$toStatusRaw] ?? (string)$toStatusRaw;
		} else {
			$toStatus = (string)$toStatusRaw;
		}
	} else {
		$fromStatus = (int)$fromStatusRaw;
		$toStatus = (int)$toStatusRaw;
	}

	$sql = 'UPDATE amazon_products SET status = :to_status WHERE warehouse_id = :wid AND status = :from_status';
	$stmt = $pdo->prepare($sql);
	$params = [
		':to_status' => $toStatus,
		':wid' => $warehouseId,
		':from_status' => $fromStatus,
	];
	$stmt->execute($params);
	$affected = (int)$stmt->rowCount();
	$pdo->commit();

	json_response(0, 'reset success', [
		'warehouse_id' => $warehouseId,
		'from_status_input' => $fromStatusRaw,
		'to_status_input' => $toStatusRaw,
		'status_is_string' => $isStringStatus,
		'status_column_type' => $colTypeRaw,
		'from_status_applied' => $fromStatus,
		'to_status_applied' => $toStatus,
		'affected_rows' => $affected,
	]);
} catch (Throwable $e) {
	if ($pdo->inTransaction()) {
		$pdo->rollBack();
	}
	log_reset_error('RESET_ERROR', [
		'warehouse_id' => $warehouseId,
		'from_status_raw' => $fromStatusRaw,
		'to_status_raw' => $toStatusRaw,
		'error' => $e->getMessage()
	]);
	http_response_code(500);
	json_response(-1, '数据库错误: ' . $e->getMessage());
}

